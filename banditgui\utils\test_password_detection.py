from banditgui.utils.password_detection import detect_password_from_output


def test_detects_valid_bandit_password():
    output = 'The password is: abcdef<PERSON><PERSON><PERSON>lmnopqrstuvwxyzABCDEF'
    assert detect_password_from_output(output) == 'abcdefghijklmnopqrstuvwxyzABCDEF'

def test_no_password_in_output():
    output = 'No password here!'
    assert detect_password_from_output(output) is None

def test_multiple_passwords_in_output():
    output = 'pw1: 12345678901234567890123456789012 pw2: abcdefghijklmnopqrstuvwxyzABCDEF'
    # Should return the first match
    assert detect_password_from_output(output) == '12345678901234567890123456789012'

def test_password_with_extra_characters():
    output = 'The password is: abcdefghijklmnopqrstuvwxyzABCDEF!'
    # Should not match because of the exclamation mark
    assert detect_password_from_output(output) is None

def test_password_at_start_of_output():
    output = 'abcdefghijklmnopqrstuvwxyzABCDEF is the password.'
    assert detect_password_from_output(output) == 'abcdefghijklmnopqrstuvwxyzABCDEF' 
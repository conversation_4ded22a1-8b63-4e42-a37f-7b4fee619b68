import json
import os
from unittest.mock import Magic<PERSON>ock

import pytest

from banditgui.app import app as flask_app
from banditgui.utils.rate_limiter import rate_limiter

# Make sure the app is in testing mode
flask_app.config.update({
    "TESTING": True,
})

@pytest.fixture
def app():
    """Create and configure a new app instance for each test."""
    flask_app.config.update({
        "TESTING": True,
        "SESSION_COOKIE_SECURE": True,
        "SESSION_COOKIE_HTTPONLY": True,
        "SESSION_COOKIE_SAMESITE": 'Lax',
        "PERMANENT_SESSION_LIFETIME": 1800,  # 30 minutes
        "SESSION_REFRESH_EACH_REQUEST": True,
    })
    yield flask_app

@pytest.fixture
def client(app):
    """A test client for the app."""
    return app.test_client()

@pytest.fixture(autouse=True)
def reset_rate_limiter():
    rate_limiter.ip_limits.clear()

# Sample data for /ask-a-pro tests
SAMPLE_ASK_A_PRO_DATA = {
    "level_name": "0",
    "level_description": "The goal of this level is to log into the game using SSH.",
    "command_history": ["ls -la", "whoami"]
}

# Path to the llm_model.json file
LLM_MODEL_CONFIG_PATH = os.path.join(os.path.dirname(__file__), '..', 'config', 'llm_model.json')

def test_server_status(client):
    """Test server status endpoint."""
    response = client.get('/server-status')
    assert response.status_code == 200
    assert response.mimetype == 'application/json'
    data = json.loads(response.data)
    assert data['status'] == 'success'

# --- Tests for /config/llm_model.json ---

def test_serve_llm_model_json_success(client):
    """Test successful serving of llm_model.json."""
    config_dir = os.path.join(flask_app.root_path, 'config')
    os.makedirs(config_dir, exist_ok=True)
    dummy_llm_json_path = os.path.join(config_dir, 'llm_model.json')
    
    expected_content = {"openai": ["gpt-4o"], "ollama": ["test-model"]}
    with open(dummy_llm_json_path, 'w') as f:
        json.dump(expected_content, f)

    response = client.get('/config/llm_model.json')

    assert response.status_code == 200
    assert response.mimetype == 'application/json'
    actual_data = json.loads(response.data)
    assert actual_data == expected_content
    
    try:
        os.remove(dummy_llm_json_path)
    except PermissionError:
        pass

def test_serve_llm_model_json_not_found(client):
    """Test serving non-existent config file."""
    response = client.get('/config/other_file.json')
    assert response.status_code == 404
    assert response.mimetype == 'application/json'
    data = json.loads(response.data)
    assert data['status'] == 'error'
    assert "File not found" in data['message']

# --- Tests for /ask-a-pro with security features ---

def test_ask_a_pro_rate_limiting(client, mocker):
    """Test rate limiting for /ask-a-pro endpoint."""
    mock_getenv = mocker.patch('banditgui.app.os.getenv')
    mock_getenv.side_effect = lambda key: "dummy_openai_key" if key == "OPENAI_API_KEY" else None
    mock_completion = mocker.patch('banditgui.app.completion')
    mock_llm_response = MagicMock()
    mock_llm_response.choices = [MagicMock()]
    mock_llm_response.choices[0].message = MagicMock()
    mock_llm_response.choices[0].message.content = "Mocked LLM advice for OpenAI"
    mock_completion.return_value = mock_llm_response
    # Make multiple requests to trigger rate limit
    for i in range(60):  # Assuming rate limit is 50 requests per minute
        response = client.post('/ask-a-pro', json={
            "llm": "openai/gpt-4o",
            **SAMPLE_ASK_A_PRO_DATA
        })
        if response.status_code == 429:
            break
    assert response.status_code == 429
    data = json.loads(response.data)
    assert data['status'] == 'error'
    assert 'rate limit exceeded' in data['message'].lower()

def test_ask_a_pro_csrf_protection(client, mocker):
    """Test CSRF protection for /ask-a-pro endpoint."""
    mock_getenv = mocker.patch('banditgui.app.os.getenv')
    mock_getenv.side_effect = lambda key: "dummy_openai_key" if key == "OPENAI_API_KEY" else None
    mock_completion = mocker.patch('banditgui.app.completion')
    mock_llm_response = MagicMock()
    mock_llm_response.choices = [MagicMock()]
    mock_llm_response.choices[0].message = MagicMock()
    mock_llm_response.choices[0].message.content = "Mocked LLM advice for OpenAI"
    mock_completion.return_value = mock_llm_response
    # Get CSRF token from test endpoint (sets session and token)
    resp = client.get('/test-csrf-token')
    csrf_token = resp.get_json()['csrf_token']
    # Test without CSRF token
    response = client.post('/ask-a-pro', json={
        "llm": "openai/gpt-4o",
        **SAMPLE_ASK_A_PRO_DATA
    })
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data['status'] == 'error'
    assert 'csrf token' in data['message'].lower()
    # Test with valid CSRF token
    headers = {'X-CSRF-Token': csrf_token}
    response = client.post('/ask-a-pro', json={
        "llm": "openai/gpt-4o",
        **SAMPLE_ASK_A_PRO_DATA
    }, headers=headers)
    assert response.status_code in [200, 401]  # 200 if API key present, 401 if not

def test_ask_a_pro_session_required(client, mocker):
    """Test session requirement for /ask-a-pro endpoint."""
    mock_getenv = mocker.patch('banditgui.app.os.getenv')
    mock_getenv.side_effect = lambda key: "dummy_openai_key" if key == "OPENAI_API_KEY" else None
    mock_completion = mocker.patch('banditgui.app.completion')
    mock_llm_response = MagicMock()
    mock_llm_response.choices = [MagicMock()]
    mock_llm_response.choices[0].message = MagicMock()
    mock_llm_response.choices[0].message.content = "Mocked LLM advice for OpenAI"
    mock_completion.return_value = mock_llm_response
    # Clear any existing session
    client.delete_cookie('session')
    response = client.post('/ask-a-pro', json={
        "llm": "openai/gpt-4o",
        **SAMPLE_ASK_A_PRO_DATA
    })
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data['status'] == 'error'
    assert 'csrf token' in data['message'].lower()

def test_ask_a_pro_success_openai(client, mocker):
    """Test successful /ask-a-pro call with OpenAI."""
    # Initialize session and get CSRF token
    client.get('/')
    with client.session_transaction() as sess:
        csrf_token = sess.get('_csrf_token')

    mock_completion = mocker.patch('banditgui.app.completion')
    mock_getenv = mocker.patch('banditgui.app.os.getenv')

    mock_getenv.side_effect = lambda key: "dummy_openai_key" if key == "OPENAI_API_KEY" else None
    mock_llm_response = MagicMock()
    mock_llm_response.choices = [MagicMock()]
    mock_llm_response.choices[0].message = MagicMock()
    mock_llm_response.choices[0].message.content = "Mocked LLM advice for OpenAI"
    mock_completion.return_value = mock_llm_response
    
    headers = {'X-CSRF-Token': csrf_token}
    response = client.post('/ask-a-pro', json={
        "llm": "openai/gpt-4o",
        **SAMPLE_ASK_A_PRO_DATA
    }, headers=headers)
    data = json.loads(response.data)

    assert response.status_code == 200
    assert data['status'] == 'success'
    assert data['advice'] == "Mocked LLM advice for OpenAI"

    mock_getenv.assert_any_call("OPENAI_API_KEY")
    mock_completion.assert_called_once()
    args, kwargs = mock_completion.call_args
    assert kwargs['model'] == "openai/gpt-4o"
    assert kwargs['api_key'] == "dummy_openai_key"

def test_ask_a_pro_invalid_input_data(client):
    """Test /ask-a-pro with missing required input data."""
    # Initialize session and get CSRF token
    client.get('/')
    with client.session_transaction() as sess:
        csrf_token = sess.get('_csrf_token')

    headers = {'X-CSRF-Token': csrf_token}
    payload = {
        "llm": "openai/gpt-4o",
        # Missing level_name, level_description
        "command_history": ["ls"]
    }
    response = client.post('/ask-a-pro', json=payload, headers=headers)
    data = json.loads(response.data)

    assert response.status_code == 400
    assert data['status'] == 'error'
    assert "Missing required data for Ask-a-Pro" in data['message']

def test_api_security_headers(client):
    """Test security headers on API responses."""
    response = client.get('/server-status')
    headers = response.headers

    assert headers.get('X-Content-Type-Options') == 'nosniff'
    assert headers.get('X-Frame-Options') == 'DENY'
    assert headers.get('X-XSS-Protection') == '1; mode=block'
    assert 'Content-Security-Policy' in headers

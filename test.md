# Diagram of the Bandit GUI

```mermaid
flowchart TB
    subgraph "Frontend (Browser)"
        direction TB
        StaticAssets["Static Assets"]:::frontend
        click StaticAssets "https://github.com/therealfredp3d/making-banditgui/tree/main/banditgui/static/"
        IndexHTML["index.html"]:::frontend
        click IndexHTML "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/templates/index.html"
        JS1["bandit-app.js"]:::frontend
        click JS1 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/static/js/bandit-app.js"
        JS2["xterm.js"]:::frontend
        click JS2 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/static/js/xterm.js"
        JS3["xterm-bandit-terminal.js"]:::frontend
        click JS3 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/static/js/xterm-bandit-terminal.js"
        JS4["bandit-terminal.js"]:::frontend
        click JS4 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/static/js/bandit-terminal.js"
        CSS1["bandit-terminal.css"]:::frontend
        click CSS1 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/static/bandit-terminal.css"
        CSS2["xterm.css"]:::frontend
        click CSS2 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/static/xterm.css"
        CSS3["xterm-custom.css"]:::frontend
        click CSS3 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/static/xterm-custom.css"
    end

    subgraph "Backend (Flask Server)"
        direction TB
        App["app.py (Routes/Controllers)"]:::backend
        click App "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/app.py"
        ChatMgr["chat_manager.py"]:::backend
        click ChatMgr "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/chat/chat_manager.py"
        SSHMgr["ssh_manager.py"]:::backend
        click SSHMgr "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/ssh/ssh_manager.py"
        TermMgr["terminal_manager.py"]:::backend
        click TermMgr "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/terminal/terminal_manager.py"
        subgraph "Config Modules"
            direction TB
            Settings["settings.py"]:::backend
            click Settings "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/config/settings.py"
            Logging["logging.py"]:::backend
            click Logging "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/config/logging.py"
            LLMModel["llm_model.json"]:::backend
            click LLMModel "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/config/llm_model.json"
        end
        subgraph "Data Utilities"
            direction TB
            Util1["get_data.py"]:::backend
            click Util1 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/utils/get_data.py"
            Util2["extract_commands.py"]:::backend
            click Util2 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/utils/extract_commands.py"
            Util3["level_info.py"]:::backend
            click Util3 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/utils/level_info.py"
            Util4["quotes.py"]:::backend
            click Util4 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/utils/quotes.py"
        end
        Exceptions["exceptions.py"]:::backend
        click Exceptions "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/exceptions.py"
    end

    subgraph "Data Store (JSON Files)"
        direction TB
        Data1["all_data.json"]:::datastore
        click Data1 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/data/all_data.json"
        Data2["commands_data.json"]:::datastore
        click Data2 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/data/commands_data.json"
        Data3["levels_info.json"]:::datastore
        click Data3 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/data/levels_info.json"
        Data4["geek_quotes.json"]:::datastore
        click Data4 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/data/geek_quotes.json"
        Data5["general_info.json"]:::datastore
        click Data5 "https://github.com/therealfredp3d/making-banditgui/blob/main/banditgui/data/general_info.json"
    end

    subgraph "External Services"
        direction TB
        SSHHost["OverTheWire Bandit SSH Host"]:::external
        LLMProv["LLM Provider / LiteLLM"]:::external
    end

    %% Connections
    StaticAssets -->|"HTTP GET Static"| App
    IndexHTML -->|"HTTP GET Index"| App
    JS1 --> App
    JS2 --> App
    CSS1 --> App

    JS1 -->|"AJAX /chat POST"| ChatMgr
    App --> ChatMgr
    ChatMgr -->|"gRPC/HTTP"| LLMProv

    JS2 -->|"WebSocket /terminal"| TermMgr
    TermMgr --"Streams I/O\n(double-headed)"--> JS2
    TermMgr -->|"uses"| SSHMgr
    SSHMgr --"SSH Channel"| SSHHost

    App -->|reads (dashed)| Data1
    App -->|reads (dashed)| Data2
    App -->|reads (dashed)| Data3
    App -->|reads (dashed)| Data4
    App -->|reads (dashed)| Data5

    %% Styles
    classDef frontend fill:#ADD8E6,stroke:#333,stroke-width:1px;
    classDef backend fill:#98FB98,stroke:#333,stroke-width:1px;
    classDef datastore fill:#FFA500,stroke:#333,stroke-width:1px;
    classDef external fill:#DDA0DD,stroke:#333,stroke-width:1px;

	%% End
```
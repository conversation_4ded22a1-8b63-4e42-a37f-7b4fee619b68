"""
JSON Schema validation utility for BanditGUI.

This module provides JSON Schema validation for various data structures.
"""

import json
from typing import Dict

from jsonschema import ValidationError, validate

from banditgui.utils.exceptions import ProgressionError


class JSONSchemaValidator:
    """Utility class for JSON Schema validation."""
    
    @staticmethod
    def validate_json_file(file_path: str) -> Dict:
        """
        Validate a JSON file against its schema.
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            Dict: Validated JSON data
            
        Raises:
            ProgressionError: If validation fails
        """
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Determine schema based on file type
            schema = JSONSchemaValidator._get_schema(file_path)
            validate(instance=data, schema=schema)
            
            return data
            
        except json.JSONDecodeError as e:
            raise ProgressionError(
                f"Invalid JSON format in {file_path}: {str(e)}",
                error_type='json_decode_error',
                file_path=file_path
            )
        except ValidationError as e:
            raise ProgressionError(
                f"JSON schema validation failed: {str(e)}",
                error_type='schema_validation_failed',
                file_path=file_path,
                validation_error=str(e)
            )
        except Exception as e:
            raise ProgressionError(
                f"Error validating JSON file {file_path}: {str(e)}",
                error_type='validation_error',
                file_path=file_path
            )
    
    @staticmethod
    def _get_schema(file_path: str) -> Dict:
        """
        Get appropriate JSON schema based on file type.
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            Dict: JSON schema
            
        Raises:
            ProgressionError: If no schema found for file type
        """
        if 'sessions' in file_path:
            return JSONSchemaValidator._get_session_schema()
        elif 'stats' in file_path:
            return JSONSchemaValidator._get_stats_schema()
        elif 'levels_info' in file_path:
            return JSONSchemaValidator._get_levels_schema()
        elif 'commands_data' in file_path:
            return JSONSchemaValidator._get_commands_schema()
        else:
            raise ProgressionError(
                f"No schema defined for file type: {file_path}",
                error_type='schema_not_found',
                file_path=file_path
            )
    
    @staticmethod
    def _get_session_schema() -> Dict:
        """Get JSON schema for session data as a mapping of session IDs to session objects."""
        return {
            "type": "object",
            "patternProperties": {
                "^[a-f0-9\\-]+$": {  # session_id pattern (UUID-like)
                    "type": "object",
                    "properties": {
                        "session_id": {"type": "string"},
                        "start_time": {"type": "string", "format": "date-time"},
                        "current_level": {"type": "integer", "minimum": 1},
                        "completed_levels": {"type": "array", "items": {"type": "integer"}},
                        "command_history": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "required": ["level", "command", "output", "timestamp"],
                                "properties": {
                                    "level": {"type": "integer", "minimum": 1},
                                    "command": {"type": "string"},
                                    "output": {"type": "string"},
                                    "timestamp": {"type": "string", "format": "date-time"}
                                }
                            }
                        },
                        "hints_used": {"type": "object"},
                        "time_spent": {"type": "object"},
                        "achievements": {"type": "array", "items": {"type": "string"}},
                        "notes": {"type": "object"}
                    },
                    "required": [
                        "session_id", "start_time", "current_level", "completed_levels",
                        "command_history", "hints_used", "time_spent", "achievements", "notes"
                    ]
                }
            },
            "additionalProperties": False
        }
    
    @staticmethod
    def _get_stats_schema() -> Dict:
        """Get JSON schema for stats data."""
        return {
            "type": "object",
            "patternProperties": {
                r"\w+": {
                    "type": "object",
                    "properties": {
                        "total_levels_completed": {"type": "integer", "minimum": 0},
                        "total_time_spent": {"type": "number", "minimum": 0},
                        "commands_mastered": {"type": "array", "items": {"type": "string"}},
                        "streak_days": {"type": "integer", "minimum": 0},
                        "last_played": {"type": "string", "format": "date-time"},
                        "favorite_levels": {"type": "array", "items": {"type": "integer"}},
                        "difficulty_preference": {
                            "type": "string",
                            "enum": ["beginner", "intermediate", "advanced"]
                        }
                    },
                    "required": [
                        "total_levels_completed", "total_time_spent", "commands_mastered",
                        "streak_days", "last_played", "favorite_levels", "difficulty_preference"
                    ]
                }
            }
        }
    
    @staticmethod
    def _get_levels_schema() -> Dict:
        """Get JSON schema for levels data."""
        return {
            "type": "object",
            "patternProperties": {
                r"\d+": {
                    "type": "object",
                    "required": ["name", "description", "goal", "hints"],
                    "properties": {
                        "name": {"type": "string"},
                        "description": {"type": "string"},
                        "goal": {"type": "string"},
                        "hints": {"type": "array", "items": {"type": "string"}},
                        "commands": {"type": "array", "items": {"type": "string"}},
                        "password": {"type": "string"}
                    }
                }
            }
        }
    
    @staticmethod
    def _get_commands_schema() -> Dict:
        """Get JSON schema for commands data."""
        return {
            "type": "object",
            "patternProperties": {
                r"\w+": {
                    "type": "object",
                    "required": ["description", "examples", "related"],
                    "properties": {
                        "description": {"type": "string"},
                        "examples": {"type": "array", "items": {"type": "string"}},
                        "related": {"type": "array", "items": {"type": "string"}},
                        "level": {"type": "integer", "minimum": 1},
                        "category": {"type": "string"}
                    }
                }
            }
        }

    @staticmethod
    def _get_single_session_schema() -> Dict:
        """Get JSON schema for a single session object."""
        return {
            "type": "object",
            "properties": {
                "session_id": {"type": "string"},
                "start_time": {"type": "string", "format": "date-time"},
                "current_level": {"type": "integer", "minimum": 1},
                "completed_levels": {"type": "array", "items": {"type": "integer"}},
                "command_history": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["level", "command", "output", "timestamp"],
                        "properties": {
                            "level": {"type": "integer", "minimum": 1},
                            "command": {"type": "string"},
                            "output": {"type": "string"},
                            "timestamp": {"type": "string", "format": "date-time"}
                        }
                    }
                },
                "hints_used": {"type": "object"},
                "time_spent": {"type": "object"},
                "achievements": {"type": "array", "items": {"type": "string"}},
                "notes": {"type": "object"}
            },
            "required": [
                "session_id", "start_time", "current_level", "completed_levels",
                "command_history", "hints_used", "time_spent", "achievements", "notes"
            ]
        }

# Initialize the validator
json_schema_validator = JSONSchemaValidator()

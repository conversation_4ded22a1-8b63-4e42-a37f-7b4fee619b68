[MASTER]
ignore=CVS
persistent=yes
load-plugins=

[MESSAGES CONTROL]
disable=all
enable=C0104,C0105,<PERSON>0112,<PERSON>0114,<PERSON>0115,<PERSON>0116,<PERSON>0117,<PERSON>0121,<PERSON>0123,<PERSON>0131,<PERSON>0132,C0200,C0201,C0202,C0203,C0204,C0205,C0206,C0207,C0208,C0209,C0301,C0302,C0303,C0304,C0305,C0321,C0325,C0327,C0328,C0401,C0402,C0403,C0410,C0411,C0412,C0413,C0414,C0415,C1802,C1803,C1804,C1805,C3001,C3002,E0100,E0101,E0102,E0103,E0104,E0105,E0106,E0107,E0108,E0110,<PERSON>0111,<PERSON>0112,<PERSON>0113,<PERSON>0114,<PERSON>01<PERSON>,<PERSON>01<PERSON>,<PERSON>0117,E0118,<PERSON>0119,E0202,<PERSON>0203,E0211,E0213,<PERSON>0236,<PERSON>0237,E0238,E0239,E0240,E0241,E0242,E0243,E0244,E0245,E0301,E0302,E0303,E0304,E0305,E0306,E0307,E0308,E0309,E0310,E0311,E0312,E0313,E0402,E0601,E0602,E0603,E0604,<PERSON>0605,E0606,E0611,C0103,E0633,E0643,E0701,E0702,E0704,E0705,E0710,E0711,E0712,E1003,E1101,E1102,E1111,E1120,E1121,E1123,E1124,E1125,E1126,E1127,E1128,E1129,E1130,E1131,E1132,E1133,E1134,E1135,E1136,E1137,E1138,E1139,E1141,E1142,E1143,E1144,E1200,E1201,E1205,E1206,E1300,E1301,E1302,E1303,E1304,E1305,E1306,E1307,E1310,E1507,E1519,E1520,E4702,E4703,F0202,I0023,I1101,R0123,R0124,R0133,R0202,R0203,R0205,R0206,R0401,R0402,R0801,R0901,R0902,R0903,R0904,R0911,R0912,R0913,R0914,R0915,R0916,R0917,R1701,R1702,R1703,R1704,R1705,R1706,R1707,R1708,R1709,R1710,R1711,R1712,R1713,R1714,R1715,R1716,R1717,R1718,R1719,R1720,R1721,R1722,R1723,R1724,R1725,R1726,R1727,R1728,R1729,R1730,R1731,R1732,R1733,R1734,R1735,R1736,R1737,W0101,W0102,W0104,W0105,W0106,W0107,W0108,W0109,W0120,W0122,W0123,W0124,W0125,W0126,W0127,W0128,W0129,W0130,W0131,W0133,W0134,W0135,W0143,W0150,W0177,W0199,W0201,W0211,W0212,W0213,W0221,W0222,W0223,W0231,W0233,W0236,W0237,W0238,W0239,W0240,W0244,W0245,W0246,W0301,W0311,W0401,W0404,W0406,W0407,W0410,W0416,W0511,W0601,W0602,W0603,W0604,W0611,W0612,W0613,W0614,W0621,W0622,W0631,W0632,W0640,W0641,W0642,W0644,W0702,W0705,W0706,W0707,W0711,W0715,W0716,W0718,W0719,W1113,W1114,W1115,W1116,W1117,W1201,W1202,W1203,W1300,W1301,W1302,W1303,W1304,W1305,W1306,W1307,W1308,W1309,W1310,W1401,W1402,W1404,W1405,W1406,W1501,W1502,W1503,W1506,W1507,W1508,W1509,W1510,W1514,W1515,W1518,W2101,W2301,W2601,W2602,W2603,W2604,W2605,W2606,W4701,W4901,W4902,W4903,W4904,W4905,W4906

[BASIC]
argument-rgx=[a-z_][a-z0-9_]{2,30}$
attr-rgx=[a-z_][a-z0-9_]{2,30}$
class-rgx=[A-Z_][a-zA-Z0-9]+$
const-rgx=(([A-Z_][A-Z0-9_]*)|(__.*__))$
function-rgx=[a-z_][a-z0-9_]{2,30}$
method-rgx=[a-z_][a-z0-9_]{2,30}$
module-rgx=(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$
variable-rgx=[a-z_][a-z0-9_]{2,30}$
inlinevar-rgx=[A-Za-z_][A-Za-z0-9_]*$
class-attribute-rgx=([A-Za-z_][A-Za-z0-9_]{2,30}|(__.*__))$

[DESIGN]
max-locals=15

[FORMAT]
max-line-length=120


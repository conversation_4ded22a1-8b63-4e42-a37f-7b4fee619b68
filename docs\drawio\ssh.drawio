<?xml version="1.0" encoding="UTF-8"?>
      <mxfile host="codeviz.app" modified="2025-07-01T14:14:08.913Z" agent="CodeViz Exporter" version="14.6.5" type="device">
        <diagram id="codeviz-diagram" name="System Diagram">
          <mxGraphModel dx="1000" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
              <mxCell id="0"/>
              <mxCell id="1" parent="0"/>
              <mxCell id="step2_subgraph_ceea2b93ee972a57c18cb80ada3f295e" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="session-wrapper-ceea2b93ee972a57c18cb80ada3f295e">
                <mxGeometry x="50" y="255" width="240" height="380" as="geometry"/>
              </mxCell>
              <mxCell id="step2_subgraph_ceea2b93ee972a57c18cb80ada3f295e_label" value="Step 2: `banditgui/ssh/__init__.py` (In Focus)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="session-wrapper-ceea2b93ee972a57c18cb80ada3f295e">
                <mxGeometry x="58" y="263" width="164" height="24" as="geometry"/>
              </mxCell>
<mxCell id="session-wrapper-ceea2b93ee972a57c18cb80ada3f295e" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="32" y="282" width="300" height="830" as="geometry"/>
              </mxCell>
              <mxCell id="session-wrapper-ceea2b93ee972a57c18cb80ada3f295e_label" value="" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="40" y="290" width="224" height="24" as="geometry"/>
              </mxCell>
              <mxCell id="applicationComponents_node_ceea2b93ee972a57c18cb80ada3f295e" value="Application Components&lt;br&gt;(Context)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="session-wrapper-ceea2b93ee972a57c18cb80ada3f295e">
                    <mxGeometry x="80" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="remoteServer_node_ceea2b93ee972a57c18cb80ada3f295e" value="Remote Server&lt;br&gt;(Context)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="session-wrapper-ceea2b93ee972a57c18cb80ada3f295e">
                    <mxGeometry x="80" y="730" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="initPy_node_ceea2b93ee972a57c18cb80ada3f295e" value="__init__.py&lt;br&gt;(file:banditgui/ssh/__init__.py)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="step2_subgraph_ceea2b93ee972a57c18cb80ada3f295e">
                    <mxGeometry x="40" y="120" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="sshManagerClass_node_ceea2b93ee972a57c18cb80ada3f295e" value="SSHManager Class&lt;br&gt;(Primary Entry Point)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="step2_subgraph_ceea2b93ee972a57c18cb80ada3f295e">
                    <mxGeometry x="40" y="280" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="search-result-ceea2b93ee972a57c18cb80ada3f295e" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="212" y="12" width="120" height="60" as="geometry"/>
                  </mxCell>
<mxCell id="terminal-ceea2b93ee972a57c18cb80ada3f295e" value="Terminal" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="332" y="272" width="120" height="60" as="geometry"/>
                  </mxCell>
              <mxCell id="edge-L_initPy_node_sshManagerClass_node_0_ceea2b93ee972a57c18cb80ada3f295e" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="initPy_node_ceea2b93ee972a57c18cb80ada3f295e" target="sshManagerClass_node_ceea2b93ee972a57c18cb80ada3f295e">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_initPy_node_sshManagerClass_node_0_ceea2b93ee972a57c18cb80ada3f295e_label" value="Exposes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_initPy_node_sshManagerClass_node_0_ceea2b93ee972a57c18cb80ada3f295e">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_applicationComponents_node_initPy_node_1_ceea2b93ee972a57c18cb80ada3f295e" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="applicationComponents_node_ceea2b93ee972a57c18cb80ada3f295e" target="initPy_node_ceea2b93ee972a57c18cb80ada3f295e">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_applicationComponents_node_initPy_node_1_ceea2b93ee972a57c18cb80ada3f295e_label" value="Imports SSHManager" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_applicationComponents_node_initPy_node_1_ceea2b93ee972a57c18cb80ada3f295e">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-L_sshManagerClass_node_remoteServer_node_2_ceea2b93ee972a57c18cb80ada3f295e" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="sshManagerClass_node_ceea2b93ee972a57c18cb80ada3f295e" target="remoteServer_node_ceea2b93ee972a57c18cb80ada3f295e">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_sshManagerClass_node_remoteServer_node_2_ceea2b93ee972a57c18cb80ada3f295e_label" value="Manages SSH Connection" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-L_sshManagerClass_node_remoteServer_node_2_ceea2b93ee972a57c18cb80ada3f295e">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-search-result-ceea2b93ee972a57c18cb80ada3f295e-to-session-wrapper-ceea2b93ee972a57c18cb80ada3f295e" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="search-result-ceea2b93ee972a57c18cb80ada3f295e" target="session-wrapper-ceea2b93ee972a57c18cb80ada3f295e">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-search-result-ceea2b93ee972a57c18cb80ada3f295e-to-session-wrapper-ceea2b93ee972a57c18cb80ada3f295e_label" value="Search Connection" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-search-result-ceea2b93ee972a57c18cb80ada3f295e-to-session-wrapper-ceea2b93ee972a57c18cb80ada3f295e">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-edge-search-to-terminal-ceea2b93ee972a57c18cb80ada3f295e" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="search-result-ceea2b93ee972a57c18cb80ada3f295e" target="terminal-ceea2b93ee972a57c18cb80ada3f295e">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-edge-search-to-terminal-ceea2b93ee972a57c18cb80ada3f295e_label" value="Terminal Connection" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-edge-search-to-terminal-ceea2b93ee972a57c18cb80ada3f295e">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
            </root>
          </mxGraphModel>
        </diagram>
      </mxfile>
<?xml version="1.0" encoding="UTF-8"?>
      <mxfile host="codeviz.app" modified="2025-07-01T13:19:41.413Z" agent="CodeViz Exporter" version="14.6.5" type="device">
        <diagram id="codeviz-diagram" name="System Diagram">
          <mxGraphModel dx="1000" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
              <mxCell id="0"/>
              <mxCell id="1" parent="0"/>
              <mxCell id="5901" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="22" y="1545.1578312295737" width="684.4734936887214" height="244.07891561478687" as="geometry"/>
              </mxCell>
              <mxCell id="5901_label" value="External Systems" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="30" y="1553.1578312295737" width="608.4734936887214" height="24" as="geometry"/>
              </mxCell>
<mxCell id="5902" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="22" y="595.1578312295737" width="712.4997988936503" height="779.0789156147869" as="geometry"/>
              </mxCell>
              <mxCell id="5902_label" value="BanditGUI Web Application&lt;br&gt;Flask, xterm.js" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="30" y="603.1578312295737" width="636.4997988936503" height="24" as="geometry"/>
              </mxCell>
              <mxCell id="5912" value="User&lt;br&gt;External Actor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="121.46050197133663" y="12" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="5913" value="Installation Script&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="183.99995977873004" y="296.07891561478687" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="5903" value="Web Server &amp;amp; API&lt;br&gt;Flask" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5902">
                    <mxGeometry x="90.44734936887212" y="305" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="5904" value="Frontend UI&lt;br&gt;JavaScript, xterm.js" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5902">
                    <mxGeometry x="62.421044163943165" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="5905" value="SSH Connection Manager&lt;br&gt;Paramiko" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5902">
                    <mxGeometry x="40" y="490" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="5906" value="Terminal Logic Manager&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5902">
                    <mxGeometry x="436.3156624591476" y="470" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="5907" value="Chat &amp;amp; AI Services&lt;br&gt;Python, LiteLLM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5902">
                    <mxGeometry x="238.1578312295738" y="480" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="5908" value="Data &amp;amp; Utility Services&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5902">
                    <mxGeometry x="464.34196766407655" y="655" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="5909" value="AI APIs&lt;br&gt;LiteLLM, OpenAI, etc." style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5901">
                    <mxGeometry x="238.1578312295738" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="5910" value="Remote SSH Servers&lt;br&gt;Paramiko Target" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5901">
                    <mxGeometry x="40" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="5911" value="Web Data Sources&lt;br&gt;Bandit Wargame Site, etc." style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="5901">
                    <mxGeometry x="436.3156624591476" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
              <mxCell id="edge-3324" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5904" target="5903">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3324_label" value="Makes API calls to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3324">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3323" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5913" target="5903">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3323_label" value="Sets up environment for" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3323">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3325" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5903" target="5904">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3325_label" value="Serves" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3325">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3326" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5903" target="5905">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3326_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3326">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3327" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5903" target="5906">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3327_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3327">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3328" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5903" target="5907">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3328_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3328">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3330" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5903" target="5908">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3330_label" value="Gets data from" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3330">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3322" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5912" target="5904">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3322_label" value="Interacts with" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3322">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3321" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5912" target="5913">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3321_label" value="Runs" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3321">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3329" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5906" target="5908">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3329_label" value="Gets level info from" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3329">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3332" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5907" target="5909">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3332_label" value="Calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3332">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3331" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5905" target="5910">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3331_label" value="Connects to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3331">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-3333" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="5908" target="5911">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-3333_label" value="Scrapes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-3333">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
            </root>
          </mxGraphModel>
        </diagram>
      </mxfile>
<?xml version="1.0" encoding="UTF-8"?>
      <mxfile host="codeviz.app" modified="2025-05-01T03:12:39.967Z" agent="CodeViz Exporter" version="14.6.5" type="device">
        <diagram id="codeviz-diagram" name="System Diagram">
          <mxGraphModel dx="1000" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
              <mxCell id="0"/>
              <mxCell id="1" parent="0"/>
              <mxCell id="1728" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1696">
                <mxGeometry x="508" y="136.03945780739355" width="260" height="260" as="geometry"/>
              </mxCell>
              <mxCell id="1728_label" value="result&lt;br&gt;terminal_manager.py" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1696">
                <mxGeometry x="516" y="144.03945780739355" width="184" height="24" as="geometry"/>
              </mxCell>
<mxCell id="3328" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="3317">
                <mxGeometry x="309" y="350.1972890369672" width="260" height="260" as="geometry"/>
              </mxCell>
              <mxCell id="3328_label" value="error_msg&lt;br&gt;ssh_manager.py" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="3317">
                <mxGeometry x="317" y="358.1972890369672" width="184" height="24" as="geometry"/>
              </mxCell>
<mxCell id="3331" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="3317">
                <mxGeometry x="309" y="316.1972890369672" width="260" height="260" as="geometry"/>
              </mxCell>
              <mxCell id="3331_label" value="error_msg&lt;br&gt;ssh_manager.py" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="3317">
                <mxGeometry x="317" y="324.1972890369672" width="184" height="24" as="geometry"/>
              </mxCell>
<mxCell id="3334" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="3317">
                <mxGeometry x="309" y="286.1972890369672" width="260" height="260" as="geometry"/>
              </mxCell>
              <mxCell id="3334_label" value="error_msg&lt;br&gt;ssh_manager.py" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="3317">
                <mxGeometry x="317" y="294.1972890369672" width="184" height="24" as="geometry"/>
              </mxCell>
<mxCell id="3351" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="3318">
                <mxGeometry x="309" y="692.4340358813279" width="260" height="260" as="geometry"/>
              </mxCell>
              <mxCell id="3351_label" value="error_msg&lt;br&gt;ssh_manager.py" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="3318">
                <mxGeometry x="317" y="700.4340358813279" width="184" height="24" as="geometry"/>
              </mxCell>
<mxCell id="3317" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="1253" y="22" width="657.1578312295737" height="1042.631324918295" as="geometry"/>
              </mxCell>
              <mxCell id="3317_label" value="connect&lt;br&gt;ssh_manager.py" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="1261" y="30" width="581.1578312295737" height="24" as="geometry"/>
              </mxCell>
<mxCell id="3318" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="22" y="195.6183734221804" width="1145.1578312295737" height="972.4340358813279" as="geometry"/>
              </mxCell>
              <mxCell id="3318_label" value="execute_command&lt;br&gt;ssh_manager.py" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="30" y="203.6183734221804" width="1069.1578312295737" height="24" as="geometry"/>
              </mxCell>
<mxCell id="1696" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="1183" y="1091.1411893701434" width="856.1578312295737" height="1356.8286139552624" as="geometry"/>
              </mxCell>
              <mxCell id="1696_label" value="execute_command&lt;br&gt;terminal_manager.py" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="1191" y="1099.1411893701434" width="780.1578312295737" height="24" as="geometry"/>
              </mxCell>
<mxCell id="1700" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="565" y="1362.318749503414" width="557.1578312295737" height="814.4734936887216" as="geometry"/>
              </mxCell>
              <mxCell id="1700_label" value="ssh_command&lt;br&gt;terminal_manager.py" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="573" y="1370.318749503414" width="481.15783122957373" height="24" as="geometry"/>
              </mxCell>
              <mxCell id="3314" value="logger&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="1981" y="736.934035881328" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1725" value="command&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="578" y="311.71048186070186" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1724" value="self&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="40" y="776.434035881328" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1726" value="cmd_parts&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="309" y="320.1183734221805" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1727" value="cmd&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="40" y="320.1183734221805" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1729" value="self&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="608" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1730" value="ssh_connected&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="40" y="1004.5918671109018" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1731" value="level_part&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="339" y="548.2762046517543" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1733" value="current_level&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="40" y="1118.6707827256887" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1734" value="level_part&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="70" y="548.2762046517543" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1732" value="self&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="389" y="434.1972890369674" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1736" value="current_level&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="40" y="662.3551202665411" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1735" value="self&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="40" y="890.5129514961149" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1738" value="current_level&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="40" y="1232.7496983404756" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1737" value="self&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="110" y="206.03945780739355" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1739" value="command&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1696">
                    <mxGeometry x="329" y="206.03945780739355" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1747" value="args&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1700">
                    <mxGeometry x="309" y="348.1578312295738" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1746" value="self&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1700">
                    <mxGeometry x="40" y="462.2367468443607" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1748" value="connect_result&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1700">
                    <mxGeometry x="40" y="576.3156624591476" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1749" value="self&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1700">
                    <mxGeometry x="309" y="576.3156624591476" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1750" value="ssh_connected&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1700">
                    <mxGeometry x="40" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1752" value="current_level&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1700">
                    <mxGeometry x="40" y="234.0789156147869" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1751" value="self&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1700">
                    <mxGeometry x="40" y="690.3945780739344" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="1753" value="args&lt;br&gt;terminal_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1700">
                    <mxGeometry x="40" y="348.1578312295738" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3324" value="self&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3317">
                    <mxGeometry x="110" y="462.2367468443607" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3326" value="client&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3317">
                    <mxGeometry x="40" y="918.5524093035082" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3325" value="self&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3317">
                    <mxGeometry x="40" y="690.3945780739344" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3327" value="e&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3317">
                    <mxGeometry x="409" y="314.1578312295738" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3329" value="error_msg&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3317">
                    <mxGeometry x="60" y="576.3156624591476" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3330" value="e&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3317">
                    <mxGeometry x="40" y="804.4734936887213" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3332" value="error_msg&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3317">
                    <mxGeometry x="40" y="348.1578312295738" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3333" value="e&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3317">
                    <mxGeometry x="90" y="234.0789156147869" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3335" value="error_msg&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3317">
                    <mxGeometry x="70" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3337" value="command&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="309" y="804.4734936887213" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3336" value="self&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="897" y="524.276204651754" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3338" value="connect_result&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="628" y="562.3025098566831" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3339" value="error_msg&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="359" y="562.3025098566831" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3340" value="error_msg&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="90" y="576.3156624591475" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3341" value="error_msg&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="40" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3342" value="error_msg&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="120" y="462.2367468443606" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3346" value="command&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="40" y="804.4734936887213" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3345" value="stderr&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="578" y="248.09206821725127" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3343" value="stdin&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="40" y="234.07891561478687" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3344" value="stdout&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="608" y="362.1709838320382" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3347" value="output&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="339" y="362.1709838320382" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3348" value="error&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="309" y="248.09206821725127" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3349" value="result&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="70" y="348.15783122957373" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3350" value="e&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="409" y="690.3945780739344" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3352" value="error_msg&lt;br&gt;ssh_manager.py" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3318">
                    <mxGeometry x="40" y="690.3945780739344" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
              <mxCell id="edge-536" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1753" target="1747">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-536_label" value="args" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-536">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-532" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1748" target="1749">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-532_label" value="self" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-532">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-514" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1726" target="1725">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-514_label" value="command" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-514">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-518" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1728" target="1729">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-518_label" value="self" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-518">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-519" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1728" target="1725">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-519_label" value="command" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-519">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-520" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1731" target="1725">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-520_label" value="command" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-520">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-525" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1739" target="1725">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-525_label" value="command" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-525">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-515" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1727" target="1726">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-515_label" value="cmd_parts" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-515">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-521" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1734" target="1731">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-521_label" value="level_part" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-521">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-535" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="1700" target="1696">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-535_label" value="execute_command" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-535">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-582" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3338" target="3336">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-582_label" value="self" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-582">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-583" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3338" target="3317">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-583_label" value="connect" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-583">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-590" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3346" target="3337">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-590_label" value="command" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-590">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-584" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3339" target="3338">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-584_label" value="connect_result" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-584">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-586" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3340" target="3339">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-586_label" value="error_msg" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-586">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-588" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3342" target="3339">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-588_label" value="error_msg" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-588">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-591" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3347" target="3344">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-591_label" value="stdout" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-591">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-592" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3348" target="3345">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-592_label" value="stderr" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-592">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-594" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3349" target="3347">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-594_label" value="output" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-594">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-596" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3349" target="3348">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-596_label" value="error" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-596">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-598" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3351" target="3350">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-598_label" value="e" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-598">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-600" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3352" target="3351">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-600_label" value="error_msg" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-600">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-581" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3318" target="3314">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-581_label" value="logger" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-581">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-572" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3328" target="3327">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-572_label" value="e" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-572">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-575" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3331" target="3327">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-575_label" value="e" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-575">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-578" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3334" target="3327">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-578_label" value="e" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-578">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-574" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3329" target="3328">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-574_label" value="error_msg" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-574">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-577" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3332" target="3328">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-577_label" value="error_msg" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-577">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-580" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3335" target="3328">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-580_label" value="error_msg" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-580">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-570" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3317" target="3314">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-570_label" value="logger" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-570">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
            </root>
          </mxGraphModel>
        </diagram>
      </mxfile>
<?xml version="1.0" encoding="UTF-8"?>
      <mxfile host="codeviz.app" modified="2025-05-01T03:10:15.266Z" agent="CodeViz Exporter" version="14.6.5" type="device">
        <diagram id="codeviz-diagram" name="System Diagram">
          <mxGraphModel dx="1000" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
              <mxCell id="0"/>
              <mxCell id="1" parent="0"/>
              <mxCell id="3576" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="3575">
                <mxGeometry x="50" y="95" width="1873.4204810661638" height="244.07891561478687" as="geometry"/>
              </mxCell>
              <mxCell id="3576_label" value="BanditGUI Web Application&lt;br&gt;Python/Flask" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="3575">
                <mxGeometry x="58" y="103" width="1797.4204810661638" height="24" as="geometry"/>
              </mxCell>
<mxCell id="3574" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="1449.104818607016" y="657" width="486.31566245914746" height="244.07891561478687" as="geometry"/>
              </mxCell>
              <mxCell id="3574_label" value="External Systems" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="1457.104818607016" y="665" width="410.31566245914746" height="24" as="geometry"/>
              </mxCell>
<mxCell id="3575" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" vertex="1" parent="1">
                <mxGeometry x="32" y="212" width="2141.5783122957378" height="359.07891561478687" as="geometry"/>
              </mxCell>
              <mxCell id="3575_label" value="Remaking-BanditGUI System" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" vertex="1" parent="1">
                <mxGeometry x="40" y="220" width="2065.5783122957378" height="24" as="geometry"/>
              </mxCell>
              <mxCell id="3577" value="User&lt;br&gt;External Actor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="1280.9469873774426" y="12" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3578" value="Developer/Admin&lt;br&gt;External Actor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="1">
                    <mxGeometry x="1915.4204810661638" y="12" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3579" value="Installation Script&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3575">
                    <mxGeometry x="1903.4204810661638" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3589" value="Bandit Wargame Server&lt;br&gt;External SSH Server" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3574">
                    <mxGeometry x="40" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3590" value="OverTheWire Website&lt;br&gt;External Web Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3574">
                    <mxGeometry x="238.1578312295738" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3580" value="Web Server &amp;amp; API&lt;br&gt;Flask" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3576">
                    <mxGeometry x="1228.9469873774426" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3581" value="Frontend Logic&lt;br&gt;JavaScript" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3576">
                    <mxGeometry x="40" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3582" value="SSH Manager&lt;br&gt;Python/Paramiko" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3576">
                    <mxGeometry x="1427.1048186070163" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3583" value="Terminal Manager&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3576">
                    <mxGeometry x="238.1578312295738" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3584" value="Chat Manager&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3576">
                    <mxGeometry x="436.3156624591476" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3585" value="Level Info Manager&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3576">
                    <mxGeometry x="1625.26264983659" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3586" value="Quote Manager&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3576">
                    <mxGeometry x="634.4734936887214" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3587" value="Configuration &amp;amp; Logging&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3576">
                    <mxGeometry x="832.6313249182951" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
<mxCell id="3588" value="Custom Exceptions&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" vertex="1" parent="3576">
                    <mxGeometry x="1030.7891561478689" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry"/>
                  </mxCell>
              <mxCell id="edge-1373" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3578" target="3579">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-1373_label" value="Runs" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-1373">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-1372" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3577" target="3580">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-1372_label" value="Interacts with GUI" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-1372">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-1374" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3582" target="3589">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-1374_label" value="Connects to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-1374">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
<mxCell id="edge-1375" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" edge="1" parent="1" source="3585" target="3590">
                  <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-1375_label" value="Fetches data from" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge-1375">
                  <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                    <mxPoint as="offset"/>
                  </mxGeometry>
                </mxCell>
            </root>
          </mxGraphModel>
        </diagram>
      </mxfile>
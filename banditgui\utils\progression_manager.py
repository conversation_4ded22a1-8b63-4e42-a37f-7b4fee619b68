import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional

from banditgui.exceptions import (
    InvalidLevelError,
    ProgressionError,
    SessionNotFoundError,
)
from banditgui.models.progression import PlayerSession, PlayerStats
from banditgui.utils.analytics import LearningAnalytics
from banditgui.utils.logging_utils import setup_logger
from banditgui.utils.validation import DataValidator


class ProgressionManager:
    def __init__(self, data_dir: str = "user_data"):
        """
        Initialize the ProgressionManager.
        
        Args:
            data_dir: Directory to store session and stats data
        
        Raises:
            OSError: If data directory cannot be created
        """
        try:
            # Setup logging with structured format
            self.logger = setup_logger("progression_manager")
            
            self.data_dir = Path(data_dir)
            self.logger.info(f"Initializing ProgressionManager with data directory: {self.data_dir}")
            
            if not self.data_dir.exists():
                self.logger.info(f"Creating data directory: {self.data_dir}")
            
            self.data_dir.mkdir(exist_ok=True)
            self.sessions_file = self.data_dir / "sessions.json"
            self.stats_file = self.data_dir / "stats.json"
            
            self.logger.info(f"Loading session data from: {self.sessions_file}")
            self.sessions = self._load_json(self.sessions_file)
            
            self.logger.info(f"Loading stats data from: {self.stats_file}")
            self.stats = self._load_json(self.stats_file)
            
            self.analytics = LearningAnalytics()
            
            self.logger.info("ProgressionManager initialization complete")
            self.logger.info(f"Loaded {len(self.sessions)} sessions and {len(self.stats)} stats records")
            
        except OSError as e:
            self.logger.error(
                f"Failed to initialize ProgressionManager: {e}",
                exc_info=True,
                extra={"error_type": "initialization_failed", "data_dir": str(self.data_dir)}
            )
            raise

    def _load_json(self, path: Path) -> Dict:
        """
        Load and validate JSON data from a file.
        
        Args:
            path: Path to the JSON file
            
        Returns:
            Dictionary containing the loaded JSON data
            
        Raises:
            ProgressionError: If file loading or validation fails
        """
        try:
            if not path.exists():
                self.logger.warning(f"File not found: {path}")
                return {}
            
            data = DataValidator.validate_json_file(str(path))
            
            # Validate data structure based on file type
            if 'sessions' in str(path):
                for session_id, session_data in data.items():
                    DataValidator.validate_session_data(session_data)
            elif 'stats' in str(path):
                for stats_id, stats_data in data.items():
                    DataValidator.validate_stats_data(stats_data)
            
            self.logger.info(f"Successfully loaded and validated JSON from {path}")
            return data
            
        except ProgressionError as e:
            self.logger.error(
                f"Failed to load and validate JSON from {path}: {str(e)}",
                exc_info=True,
                extra={"error_type": e.error_type}
            )
            raise

    def _save_json(self, path: Path, data: Dict) -> None:
        """
        Save and validate data to a JSON file.
        
        Args:
            path: Path to save the JSON file
            data: Dictionary to save as JSON
            
        Raises:
            ProgressionError: If file saving or validation fails
        """
        try:
            # Validate data structure based on file type
            if 'sessions' in str(path):
                for session_id, session_data in data.items():
                    DataValidator.validate_session_data(session_data)
            elif 'stats' in str(path):
                for stats_id, stats_data in data.items():
                    DataValidator.validate_stats_data(stats_data)
                    
            with open(path, 'w') as f:
                json.dump(data, f, default=str, indent=2)
                self.logger.debug(f"Saved JSON to {path}")
        except ProgressionError as e:
            self.logger.error(
                f"Failed to save JSON to {path}: {str(e)}",
                exc_info=True,
                extra={"error_type": e.error_type}
            )
            raise
        except OSError as e:
            self.logger.error(
                f"Failed to save JSON to {path}: {e}",
                exc_info=True,
                extra={"error_type": 'file_io_error'}
            )
            raise ProgressionError(
                f"Failed to save JSON data: {e}",
                error_type='data_save_failed',
                path=str(path)
            )

    def start_session(self, session_id: str) -> PlayerSession:
        """
        Start a new session for a player.
        
        Args:
            session_id: Unique identifier for the session
            
        Returns:
            PlayerSession object representing the new session
            
        Raises:
            ValueError: If session_id is not a valid string
            OSError: If session data cannot be saved
        """
        if not isinstance(session_id, str) or not session_id:
            raise ValueError("Session ID must be a non-empty string")
        
        try:
            self.logger.info(f"Starting new session with ID: {session_id}")
            
            now = datetime.now().isoformat()
            session = PlayerSession(
                session_id=session_id,
                start_time=now,
                current_level=1,
                completed_levels=[],
                command_history=[],
                hints_used={},
                time_spent={},
                achievements=[],
                notes={}
            )
            
            self.logger.debug(f"Created session object: {session.__dict__}")
            
            self.sessions[session_id] = session.__dict__
            self._save_json(self.sessions_file, self.sessions)

            # Create default stats for the new session
            default_stats = {
                'total_levels_completed': 0,
                'total_time_spent': 0.0,
                'commands_mastered': [],
                'streak_days': 0,
                'last_played': now,
                'favorite_levels': [],
                'difficulty_preference': 'beginner'
            }
            self.stats[session_id] = default_stats
            self._save_json(self.stats_file, self.stats)
            
            self.logger.info(f"Successfully saved session data for {session_id}")
            self.logger.info(f"Total sessions now: {len(self.sessions)}")
            
            return session
            
        except OSError as e:
            self.logger.error(
                f"Failed to save session data for {session_id}: {e}",
                exc_info=True
            )
            raise ProgressionError(
                f"Failed to save session data for {session_id}",
                error_type='data_save_failed',
                session_id=session_id
            )
        except Exception as e:
            self.logger.error(
                f"Unexpected error starting session {session_id}: {e}",
                exc_info=True
            )
            raise ProgressionError(
                f"Unexpected error starting session: {str(e)}",
                error_type='unexpected_error',
                session_id=session_id
            )

    def update_level_progress(self, session_id: str, level: int, command: str, output: str) -> None:
        """
        Update progress for a specific level.
        
        Args:
            session_id: ID of the session
            level: Current level number
            command: Command executed
            output: Command output
            
        Raises:
            SessionNotFoundError: If session does not exist
            InvalidLevelError: If level number is invalid
            ValueError: If command or output is invalid
            OSError: If progress data cannot be saved
        """
        self.logger.info(f"Updating progress for session {session_id} at level {level}")
        
        if not isinstance(level, int) or level < 1:
            self.logger.error(f"Invalid level number: {level}")
            raise InvalidLevelError(level)
        
        if not isinstance(command, str) or not command:
            self.logger.error(f"Invalid command for session {session_id}: empty string")
            raise ValueError("Command must be a non-empty string")
        
        if not isinstance(output, str):
            self.logger.error(f"Invalid output type for session {session_id}: {type(output)}")
            raise ValueError("Output must be a string")
        
        try:
            session = self.sessions.get(session_id)
            if not session:
                self.logger.error(f"Session not found: {session_id}")
                raise SessionNotFoundError(session_id)
            
            progress_entry = {
                'level': level,
                'command': command,
                'output': output,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.debug(f"Adding progress entry: {progress_entry}")
            
            session['command_history'].append(progress_entry)
            self.sessions[session_id] = session
            
            self.logger.debug(f"Session history length: {len(session['command_history'])}")
            
            self._save_json(self.sessions_file, self.sessions)
            
            self.logger.info(f"Successfully updated progress for session {session_id}")
            self.logger.info(f"Total progress entries: {len(session['command_history'])}")
            
        except SessionNotFoundError as e:
            self.logger.error(f"Failed to update progress: {str(e)}", exc_info=True)
            raise
        except OSError as e:
            self.logger.error(
                f"Failed to save progress data for {session_id}: {e}",
                exc_info=True
            )
            raise ProgressionError(
                f"Failed to save progress data for {session_id}",
                error_type='data_save_failed',
                session_id=session_id
            )
        except Exception as e:
            self.logger.error(
                f"Unexpected error updating progress for {session_id}: {e}",
                exc_info=True
            )
            raise ProgressionError(
                f"Unexpected error updating progress: {str(e)}",
                error_type='unexpected_error',
                session_id=session_id
            )

    def complete_level(self, session_id: str, level: int, password: str, time_taken: float) -> None:
        """
        Mark a level as completed.
        
        Args:
            session_id: ID of the session
            level: Level number completed
            password: Recovered password
            time_taken: Time taken to complete level in seconds
            
        Raises:
            SessionNotFoundError: If session does not exist
            InvalidLevelError: If level number is invalid
            ValueError: If time_taken is negative
            OSError: If session data cannot be saved
        """
        if not isinstance(level, int) or level < 1:
            raise InvalidLevelError(level)
        
        if not isinstance(password, str) or not password:
            raise ValueError("Password must be a non-empty string")
        
        if not isinstance(time_taken, (int, float)) or time_taken < 0:
            raise ValueError("Time taken must be a non-negative number")
        
        try:
            session = self.sessions.get(session_id)
            if not session:
                raise SessionNotFoundError(session_id)
            
            session['completed_levels'].append(level)
            session['time_spent'][str(level)] = time_taken
            session['current_level'] = level + 1
            
            stats = self.stats.get(session_id, {
                'total_levels_completed': 0,
                'total_time_spent': 0.0,
                'commands_mastered': [],
                'streak_days': 0,
                'last_played': datetime.now().isoformat(),
                'favorite_levels': [],
                'difficulty_preference': 'beginner'
            })
            
            stats['total_levels_completed'] = len(session['completed_levels'])
            stats['total_time_spent'] = sum(float(t) for t in session['time_spent'].values())
            stats['last_played'] = datetime.now().isoformat()
            
            self.stats[session_id] = stats
            self._save_json(self.stats_file, self.stats)
            
            self.sessions[session_id] = session
            self._save_json(self.sessions_file, self.sessions)
            
            self.logger.info(f"Level {level} completed for session {session_id}")
            
        except SessionNotFoundError:
            self.logger.error(f"Failed to complete level: {session_id} not found")
            raise
        except Exception as e:
            self.logger.error(f"Failed to complete level {level} for {session_id}: {e}")
            raise

    def get_session_progress(self, session_id: str) -> Dict:
        """
        Get progress information for a session.
        
        Args:
            session_id: ID of the session
            
        Returns:
            Dictionary containing session progress
            
        Raises:
            SessionNotFoundError: If session does not exist
        """
        try:
            session = self.sessions.get(session_id)
            if not session:
                raise SessionNotFoundError(f"Session {session_id} not found")
                
            return {
                'current_level': session['current_level'],
                'completed_levels': session['completed_levels'],
                'total_levels_completed': len(session['completed_levels'])
            }
        except SessionNotFoundError:
            self.logger.error(f"Failed to get progress: {session_id} not found")
            raise
        except Exception as e:
            self.logger.error(f"Failed to get progress for {session_id}: {e}")
            raise

    def get_player_stats(self, session_id: str) -> Optional[PlayerStats]:
        """
        Get player statistics.
        
        Args:
            session_id: ID of the session
            
        Returns:
            PlayerStats object or None if session not found
        """
        try:
            stats = self.stats.get(session_id)
            if not stats:
                self.logger.warning(f"No stats found for session {session_id}")
                return None
                
            return PlayerStats(
                total_levels_completed=stats.get('total_levels_completed', 0),
                total_time_spent=stats.get('total_time_spent', 0),
                commands_mastered=stats.get('commands_mastered', []),
                streak_days=stats.get('streak_days', 0),
                last_played=datetime.fromisoformat(stats.get('last_played', datetime.now().isoformat())),
                favorite_levels=stats.get('favorite_levels', []),
                difficulty_preference=stats.get('difficulty_preference', 'beginner')
            )
        except Exception as e:
            self.logger.error(f"Failed to get player stats for {session_id}: {e}")
            raise

    def get_detailed_stats(self, session_id: str) -> Optional[Dict]:
        """
        Get detailed statistics including command analysis.
        
        Args:
            session_id: ID of the session
            
        Returns:
            Dictionary containing detailed statistics
            
        Raises:
            SessionNotFoundError: If session does not exist
        """
        try:
            session = self.sessions.get(session_id)
            stats = self.stats.get(session_id)
            if not session or not stats:
                raise SessionNotFoundError(f"Session {session_id} not found")
                
            command_analysis = self.analytics.analyze_command_patterns(
                session.get('command_history', [])
            )
            
            return {
                'stats': stats,
                'command_analysis': command_analysis
            }
        except SessionNotFoundError:
            self.logger.error(f"Failed to get detailed stats: {session_id} not found")
            raise
        except Exception as e:
            self.logger.error(f"Failed to get detailed stats for {session_id}: {e}")
            raise
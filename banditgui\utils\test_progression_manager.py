import shutil
import tempfile

import pytest

from banditgui.utils.progression_manager import ProgressionManager


@pytest.fixture
def temp_data_dir():
    d = tempfile.mkdtemp()
    yield d
    shutil.rmtree(d)

def test_complete_level_stores_progress_and_password(temp_data_dir):
    pm = ProgressionManager(data_dir=temp_data_dir)
    session_id = 'testsession'
    pm.start_session(session_id)
    # Simulate completing level 1
    pm.complete_level(session_id, 1, 'testpassword1234567890123456789012', 12.5)
    # Reload manager to check persistence
    pm2 = ProgressionManager(data_dir=temp_data_dir)
    s = pm2.sessions[session_id]
    assert 1 in s['completed_levels']
    assert s['current_level'] == 2
    assert s['time_spent']['1'] == 12.5
    # Stats should be updated
    stats = pm2.stats[session_id]
    assert stats['total_levels_completed'] == 1
    assert stats['total_time_spent'] >= 12.5

def test_complete_level_idempotent(temp_data_dir):
    pm = ProgressionManager(data_dir=temp_data_dir)
    session_id = 'testsession2'
    pm.start_session(session_id)
    pm.complete_level(session_id, 1, 'pw', 10.0)
    pm.complete_level(session_id, 1, 'pw', 5.0)  # Should not duplicate level
    s = pm.sessions[session_id]
    assert s['completed_levels'].count(1) == 1
    # Time should be updated to last call
    assert s['time_spent']['1'] == 5.0 
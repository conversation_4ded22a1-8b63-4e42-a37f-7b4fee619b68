/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Primary colors - Modern dark theme with better contrast */
    --primary-dark: #181818;
    --primary-medium: #222222;
    --primary-light: #282828;

    /* Accent colors - More vibrant and accessible */
    --accent-primary: #4A90E2;
    --accent-secondary: #50E3C2;
    --accent-tertiary: #50E3C2;

    /* UI colors - Brighter and more distinct */
    --success: #2ECC71;
    --warning: #F39C12;
    --info: #3498DB;
    --error: #E74C3C;

    /* Text colors - Improved contrast */
    --text-primary: #F5F5F5;
    --text-secondary: #CCCCCC;
    --text-muted: #A0A0A0;

    /* Border colors */
    --border-color: #333333;

    /* Shadow - Slightly softer for better readability */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
}

body, .left, #chat-messages, #chat-input, .assistant-message, .user-message, .system-message {
    font-family: 'Inter', 'Roboto', 'Open Sans', -apple-system, BlinkMacSystemFont, sans-serif;
}

body {
    background-color: var(--primary-dark);
    color: var(--text-primary);
    height: 100vh;
    overflow: hidden;
    line-height: 1.6;
}

.container {
    display: flex;
    height: 100vh;
    position: relative;
}

/* Panel Styles */
.panel {
    padding: 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.left {
    width: 50%;
    background: var(--primary-medium);
    border-right: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    z-index: 10;
}

.right {
    flex: 1;
    background: var(--primary-dark);
    color: var(--text-primary);
    display: flex;
    flex-direction: column;
}

/* Border between panels */
.left {
    border-right: 2px solid var(--border-color);
}

/* Left Panel - Chat */
.left h3, .terminal-header h3 {
    padding: 15px;
    background-color: var(--primary-light);
    color: var(--text-primary);
    margin: 0;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-sm);
    flex-shrink: 0; /* Added to prevent header from shrinking */
}

#chat-container {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden; /* Hide overflow to prevent scrollbar on the container itself */
}

#chat-messages {
    flex-grow: 1;
    overflow-y: auto; /* Enable vertical scrolling */
    padding: 20px;
    scroll-behavior: smooth;
    min-height: 0; /* Allow it to shrink if needed, but grow to fill space */
    /* Add a max-height to ensure it doesn't push the input off-screen */
    max-height: calc(100vh - 120px); /* Adjust 120px based on header and input container height */
}

.system-message {
    background-color: rgba(52, 152, 219, 0.15);
    border-left: 4px solid var(--info);
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 6px 6px 0;
    box-shadow: var(--shadow-sm);
}

.user-message {
background-color: color-mix(in srgb, var(--accent-primary) 10%, transparent);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(74, 144, 226, 0.2);
}

.user-message .user-label {
    color: var(--accent-primary);
    font-weight: bold;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
}

.assistant-message {
background-color: color-mix(in srgb, var(--accent-secondary) 10%, transparent);
    border-left: 4px solid var(--accent-secondary);
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 6px 6px 0;
    box-shadow: var(--shadow-sm);
}

.assistant-message .assistant-label {
    color: var(--accent-secondary);
    font-weight: bold;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
}

.mentor-message {
    background-color: #e9ecef; /* Light gray background */
    border-left: 4px solid #4CAF50; /* Green accent bar */
    padding: 15px; /* Consistent padding */
    margin-bottom: 20px; /* Consistent margin */
    border-radius: 0 6px 6px 0; /* Consistent radius */
    box-shadow: var(--shadow-sm);
    color: #333; /* Darker text for better contrast on light background */
}

.mentor-message .mentor-label {
    font-weight: bold;
    color: #4CAF50; /* Green label text */
    margin-bottom: 8px; /* Consistent margin */
    letter-spacing: 0.5px; /* Consistent spacing */
}

.mentor-message .mentor-label .fas { /* Style for Font Awesome icon */
    margin-right: 8px;
}

.mentor-message .message-content {
    /* Add specific styles if needed, otherwise inherits from .mentor-message */
    /* Ensuring text color is explicitly set for content if needed */
    color: #333; 
}

#chat-input-container {
    display: flex;
    padding: 15px;
    background-color: var(--primary-light);
    border-top: 1px solid var(--border-color);
    width: 100%;
    z-index: 20;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    flex-shrink: 0;
    /* Removed margin-top: auto; and position: relative; as they might interfere with flex layout */
    background-color: #333;
    border-top: 2px solid #4A90E2;
}

#chat-input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    border-radius: 6px 0 0 6px; /* Updated border-radius */
background-color: var(--primary-medium); /* Or a new variable e.g. --input-bg */
    color: var(--text-primary);
    outline: none;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

#chat-input:focus {
    background-color: #242424; /* Updated background-color */
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(74, 144, 226, 0.4); /* Updated box-shadow */
}

#chat-input::placeholder {
    color: var(--text-muted);
}

#chat-submit {
    padding: 12px 18px;
    background-color: var(--accent-primary);
    color: var(--text-primary);
    border: none;
    border-radius: 0 6px 6px 0; /* Updated border-radius */
    cursor: pointer;
    transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.2s ease; /* Ensured smooth transition */
    box-shadow: var(--shadow-sm);
}

#chat-submit:hover {
    background-color: var(--accent-secondary); /* Kept as per instruction */
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

#chat-submit:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

/* Right Panel - Terminal */
.terminal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--primary-light);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    padding-right: 15px; /* Add padding to the right for spacing */
}

.header-right-controls {
    display: flex;
    align-items: center;
    gap: 15px; /* Space between dropdown and status */
}

#ask-a-pro-container {
    /* No specific positioning needed here, as it's now part of a flex container */
}

.ask-a-pro-button {
    padding: 8px 12px;
    background-color: var(--accent-tertiary);
    color: var(--text-primary);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.2s ease;
    box-shadow: var(--shadow-sm);
}

.ask-a-pro-button:hover {
    background-color: color-mix(in srgb, var(--accent-tertiary) 80%, black);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.ask-a-pro-button:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

.llm-dropdown {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--primary-medium);
    color: var(--text-primary);
    font-size: 14px;
    font-family: 'Inter', sans-serif;
    cursor: pointer;
    outline: none;
    appearance: none; /* Remove default dropdown arrow */
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2020%2020%22%20fill%3D%22%23F5F5F5%22%3E%3Cpath%20d%3D%22M5.293%207.293a1%201%200%20011.414%200L10%2010.586l3.293-3.293a1%201%200%20111.414%201.414l-4%204a1%201%200%2001-1.414%200l-4-4a1%201%200%20010-1.414z%22%2F%3E%3C%2Fsvg%3E');
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    transition: all 0.2s ease;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.llm-dropdown:hover {
    background-color: #242424;
    border-color: var(--accent-primary);
}

.llm-dropdown:focus {
    border-color: var(--accent-primary);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(74, 144, 226, 0.4);
}

.connection-status {
    display: flex;
    align-items: center;
}

#status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.connected {
    background-color: var(--success);
    box-shadow: 0 0 8px var(--success);
    animation: pulse 2s infinite;
}

.disconnected {
    background-color: var(--error);
    box-shadow: 0 0 8px var(--error);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 201, 240, 0.7);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(76, 201, 240, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 201, 240, 0);
    }
}

#status-text {
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

#terminal-container {
    flex: 1;
    /* padding: 0; */ /* Removed to allow xterm-custom.css to handle padding */
    background: var(--primary-dark); /* Updated background to match xterm-custom and use variable */
    overflow: hidden;
    border-radius: 0 0 6px 6px; /* Updated border-radius */
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.terminal-footer {
    padding: 12px;
    background-color: var(--primary-light);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
}

#connect-button {
    padding: 10px 20px;
    background-color: var(--success);
    color: var(--text-primary); /* Updated to use semantic variable */
    border: none;
    border-radius: 6px; /* Updated border-radius */
    cursor: pointer;
    transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.2s ease; /* Ensured smooth transition */
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

#connect-button:hover {
    background-color: #27AE60; /* Updated hover background-color */
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

#connect-button:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

#connect-button.disconnect {
    background-color: var(--error);
}

#connect-button.disconnect:hover {
    background-color: #C0392B; /* Updated hover background-color for disconnect */
}

/* Code formatting */
code, pre {
    background-color: rgba(26, 26, 46, 0.7);
    padding: 3px 6px;
    border-radius: 4px;
    font-family: "Fira Code", "Cascadia Code", "Source Code Pro", Consolas, "DejaVu Sans Mono", monospace;
    font-size: 0.9em;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: rgba(26, 26, 46, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(74, 144, 226, 0.4); /* Updated background color */
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(74, 144, 226, 0.6); /* Updated hover background color */
    border: 2px solid transparent;
    background-clip: padding-box;
}

/* Lists in chat */
#chat-messages ul {
    margin-left: 20px;
    margin-bottom: 10px;
}

#chat-messages li {
    margin-bottom: 5px;
}

/* Paragraphs in chat */
#chat-messages p {
    margin-bottom: 10px;
    line-height: 1.5;
}

/* Level information formatting */
.level-info {
    border-left: 4px solid var(--accent-tertiary);
    padding-left: 10px;
    margin: 15px 0;
    background-color: rgba(80, 227, 194, 0.15); /* Updated background-color */
    border-radius: 0 6px 6px 0; /* Updated border-radius */
    padding: 18px;
    box-shadow: var(--shadow-sm); /* Updated box-shadow */
}

.level-hint {
    border-left: 4px solid var(--warning);
    padding-left: 10px;
    margin: 15px 0;
    background-color: rgba(243, 156, 18, 0.1); /* Updated background-color */
    border-radius: 0 6px 6px 0; /* Updated border-radius */
    padding: 18px;
    box-shadow: var(--shadow-sm); /* Updated box-shadow */
}

.hint-prompt {
    margin-top: 15px;
    font-style: italic;
    color: var(--text-muted);
    font-size: 0.9em;
    border-top: 1px solid var(--border-color);
    padding-top: 10px;
}

.level-info h4 {
    color: var(--accent-tertiary);
    margin-bottom: 12px;
    font-size: 1.3em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.level-info strong {
    color: var(--info);
    display: block;
    margin-top: 18px;
    margin-bottom: 8px;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.level-info p {
    margin-bottom: 10px;
    line-height: 1.5;
}

.level-info a {
    color: var(--accent-secondary);
    text-decoration: none;
    border-bottom: 1px dotted var(--accent-secondary);
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
}

.level-info a:hover {
    color: var(--accent-primary);
    border-bottom: 1px solid var(--accent-primary);
    background-color: rgba(67, 97, 238, 0.1);
}

.level-info pre {
background-color: var(--pre-bg, rgba(26, 26, 46, 0.7)); /* Suggest defining --pre-bg in :root */
    padding: 15px;
    border-radius: 6px; /* Updated border-radius */
    overflow-x: auto;
    margin: 15px 0;
    font-family: "Fira Code", "Cascadia Code", "Source Code Pro", Consolas, "DejaVu Sans Mono", monospace;
    border-left: 3px solid var(--success);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

/* Error messages */
.error-message {
    color: var(--error);
    font-style: italic;
    padding: 8px 12px;
    background-color: rgba(230, 57, 70, 0.1);
    border-radius: 4px;
    margin: 8px 0;
    border-left: 3px solid var(--error);
}

/* Success messages */
.success-message {
    color: var(--success);
    font-style: italic;
    padding: 8px 12px;
    background-color: rgba(76, 201, 240, 0.1);
    border-radius: 4px;
    margin: 8px 0;
    border-left: 3px solid var(--success);
}

/* Start Game Button */
.start-game-container {
    display: flex;
    justify-content: center;
    margin: 20px 0 10px 0;
}

.start-game-button {
    padding: 12px 24px;
    background-color: var(--warning);
    color: var(--text-primary);
    border: none;
    border-radius: 6px; /* Updated border-radius */
    cursor: pointer;
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 0.5px;
    transition: background-color 0.3s ease, box-shadow 0.3s ease, transform 0.3s ease; /* Ensured smooth transition */
    box-shadow: var(--shadow-sm); /* Updated box-shadow */
    text-transform: uppercase;
}

.start-game-button:hover {
background-color: var(--warning-darker); /* Suggest defining --warning-darker: #D88A0B; in :root */
    transform: translateY(-2px);
    box-shadow: var(--shadow-md); /* Updated hover box-shadow */
}

.start-game-button:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }

    .left, .right {
        flex: none;
        height: 50vh;
    }

    #chat-messages {
        max-height: calc(50vh - 120px);
    }
}

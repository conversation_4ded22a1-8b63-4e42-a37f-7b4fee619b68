<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.1.1 Chrome/132.0.6834.210 Electron/34.3.3 Safari/537.36" version="26.1.1">
  <diagram id="codeviz-diagram" name="System Diagram">
    <mxGraphModel dx="1488" dy="764" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0" adaptiveColors="none">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="5643" value="" style="html=1;container=1;fillColor=#A8DADC;strokeColor=#457B9D;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0;rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="1" vertex="1">
          <mxGeometry x="139.9986445184836" y="940" width="486.31566245914763" height="244.07891561478687" as="geometry" />
        </mxCell>
        <mxCell id="5653" value="LLM APIs&lt;br&gt;OpenAI, Ollama, etc." style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="5643" vertex="1">
          <mxGeometry x="238.1578312295738" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5654" value="Remote SSH Servers&lt;br&gt;SSH Protocol" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="5643" vertex="1">
          <mxGeometry x="40" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5643_label" value="External Systems" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=#99FFFF;spacing=2;rounded=0;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacingLeft=0;spacingBottom=21;" parent="1" vertex="1">
          <mxGeometry x="159.9986445184836" y="960" width="410.31566245914763" height="24" as="geometry" />
        </mxCell>
        <mxCell id="5644" value="" style="html=1;container=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0;rounded=1;labelBackgroundColor=#99FFFF;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;fillColor=#f5f5f5;strokeColor=#666666;gradientColor=#0000FF;gradientDirection=radial;perimeterSpacing=5;glass=1;shadow=1;sketch=1;curveFitting=1;jiggle=2;" parent="1" vertex="1">
          <mxGeometry x="110" y="250" width="1150" height="620" as="geometry" />
        </mxCell>
        <mxCell id="5645" value="Web Server&lt;br&gt;Flask/Python" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="5644" vertex="1">
          <mxGeometry x="103.05918671109018" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5646" value="Chat Service&lt;br&gt;Python" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="5644" vertex="1">
          <mxGeometry x="271.2178312295738" y="378" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5647" value="SSH Service&lt;br&gt;Python/Paramiko" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="5644" vertex="1">
          <mxGeometry x="5.740000000000002" y="305" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5648" value="Terminal Service&lt;br&gt;Python" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="5644" vertex="1">
          <mxGeometry x="470.3156624591476" y="305" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5649" value="Application Configuration&lt;br&gt;Python" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="5644" vertex="1">
          <mxGeometry x="173.89469873774428" y="500" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5650" value="Data Utilities&lt;br&gt;Python" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="5644" vertex="1">
          <mxGeometry x="582.8982728690055" y="491" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5651" value="Static Assets&lt;br&gt;JavaScript, CSS" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="5644" vertex="1">
          <mxGeometry x="702.47" y="270" width="168.16" height="50" as="geometry" />
        </mxCell>
        <mxCell id="5652" value="HTML Templates&lt;br&gt;HTML/Jinja2" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="5644" vertex="1">
          <mxGeometry x="900.63" y="270" width="168.16" height="50" as="geometry" />
        </mxCell>
        <mxCell id="5644_label" value="BanditGUI Web Application&lt;br&gt;Python/Flask" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=#99FFFF;spacing=2;rounded=0;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacingLeft=0;spacingBottom=21;" parent="1" vertex="1">
          <mxGeometry x="115.09864451848361" y="260" width="1072.789156147869" height="24" as="geometry" />
        </mxCell>
        <mxCell id="5655" value="User&lt;br&gt;External Actor" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="1" vertex="1">
          <mxGeometry x="200.1578312295738" y="12" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5656" value="Installer Script&lt;br&gt;Python" style="rounded=0;html=1;fillColor=#A8DADC;labelBackgroundColor=#99FFFF;strokeColor=#457B9D;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="1" vertex="1">
          <mxGeometry x="760" y="100" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="edge-2758" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5655" target="5645" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2758_label" value="interacts with" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2758" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2749" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5645" target="5646" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2749_label" value="routes to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2749" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="56" y="14" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2750" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5645" target="5647" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2750_label" value="routes to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2750" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-18" y="-7" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2751" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5645" target="5648" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2751_label" value="routes to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2751" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-86" y="16" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2753" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5645" target="5649" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2753_label" value="reads" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2753" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="28" y="-56" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2752" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5645" target="5650" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2752_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2752" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="423" y="78" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2747" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5645" target="5651" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2747_label" value="serves" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2747" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="266" y="78" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2748" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5645" target="5652" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2748_label" value="renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2748" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-46" y="78" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2754" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5646" target="5649" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2754_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2754" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2759" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5646" target="5653" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2759_label" value="calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2759" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2755" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5647" target="5649" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2755_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2755" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="11" y="13" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2760" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5647" target="5654" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2760_label" value="connects to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2760" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2756" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5648" target="5649" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2756_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2756" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-11" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-2757" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#457B9D;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=2;labelBackgroundColor=#99FFFF;labelBorderColor=none;fontColor=#333333;fontSize=19;fontStyle=1;textShadow=1;spacingLeft=0;spacingBottom=21;" parent="1" source="5648" target="5650" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-2757_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;labelBackgroundColor=#99FFFF;fontColor=#333333;fontSize=19;fontStyle=1;labelBorderColor=none;textShadow=1;whiteSpace=wrap;spacing=2;spacingLeft=0;spacingBottom=21;" parent="edge-2757" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="14" y="18" as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

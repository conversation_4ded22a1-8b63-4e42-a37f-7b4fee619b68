"""
API key management utility for BanditGUI.

This module handles secure storage, validation, and rotation of API keys.
"""

import hashlib
import os
import secrets
import time
from typing import Dict, Optional

from banditgui.config.logging_config import logger
from banditgui.utils.security_logger import security_logger


class APIKeyManager:
    """
    Secure API key management class.
    """
    def __init__(self, rotation_interval: int = 86400):  # 24 hours default rotation
        """
        Initialize the API key manager.
        
        Args:
            rotation_interval: Number of seconds before API key rotation
        """
        self.rotation_interval = rotation_interval
        self.keys: Dict[str, Dict] = {}  # Store hashed keys with metadata
        self._load_keys()

    def _load_keys(self):
        """
        Load API keys from environment variables and hash them.
        """
        providers = [
            'OPENAI', 'GEMINI', 'ANTHROPIC', 'COHERE', 'MISTRAL',
            'PERPLEXITY', 'DEEPSEEK', 'GROQ', 'OPENROUTER', 'LLM'
        ]

        for provider in providers:
            key = os.getenv(f"{provider}_API_KEY")
            if key:
                hashed_key, salt = self._hash_key(key)
                self.keys[provider] = {
                    'hashed_key': hashed_key,
                    'salt': salt,
                    'last_rotated': time.time(),
                    'provider': provider
                }

    def _hash_key(self, key: str) -> tuple:
        """
        Securely hash an API key.
        
        Returns:
            tuple: (hashed_key, salt) for storage and verification
        """
        salt = secrets.token_hex(16)
        hashed = hashlib.pbkdf2_hmac(
            'sha256',
            key.encode('utf-8'),
            salt.encode('utf-8'),
            100000
        )
        return hashed.hex(), salt

    def validate_key(self, provider: str, key: str) -> bool:
        """
        Validate an API key for a specific provider.
        
        Args:
            provider: Provider name (e.g., 'OPENAI', 'GEMINI')
            key: API key to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if provider not in self.keys:
            return False

        stored_hash = self.keys[provider]['hashed_key']
        salt = self.keys[provider]['salt']
        test_hash = hashlib.pbkdf2_hmac(
            'sha256',
            key.encode('utf-8'),
            salt.encode('utf-8'),
            100000
        ).hex()
        return test_hash == stored_hash

    def get_key(self, provider: str) -> Optional[str]:
        """
        Get a validated API key for a provider with security logging.
        
        Args:
            provider: Provider name
            
        Returns:
            str: Validated API key or None if not found/invalid
        """
        if provider not in self.keys:
            security_logger.log_authentication(
                provider=provider,
                status="failure",
                details={
                    "reason": "provider_not_found",
                    "provider": provider
                }
            )
            return None

        # Check if key needs rotation
        if time.time() - self.keys[provider]['last_rotated'] > self.rotation_interval:
            self._rotate_key(provider)
            security_logger.log_authentication(
                provider=provider,
                status="success",
                details={
                    "action": "key_rotation",
                    "provider": provider
                }
            )

        key = os.getenv(f"{provider}_API_KEY")
        if not key:
            security_logger.log_authentication(
                provider=provider,
                status="failure",
                details={
                    "reason": "key_not_configured",
                    "provider": provider
                }
            )
            return None

        security_logger.log_authentication(
            provider=provider,
            status="success",
            details={
                "action": "key_retrieval",
                "provider": provider
            }
        )
        return key

    def _rotate_key(self, provider: str):
        """Log key rotation request - actual rotation handled externally."""
        logger.warning(f"API key rotation requested for {provider} - implement external rotation")
        security_logger.log_security_event(
            "api_key_rotation_requested",
            {"provider": provider, "timestamp": time.time()}
        )

    def is_key_valid(self, provider: str) -> bool:
        """
        Check if a provider's API key is valid and properly configured.
        
        Args:
            provider: Provider name
            
        Returns:
            bool: True if valid and configured, False otherwise
        """
        if provider not in self.keys:
            return False
        
        key = os.getenv(f"{provider}_API_KEY")
        return key is not None and self.validate_key(provider, key)

# Initialize API key manager with 24-hour rotation interval
api_key_manager = APIKeyManager()

# SSH Connection Settings
SSH_HOST="bandit.labs.overthewire.org"
SSH_PORT="2220"
SSH_USERNAME="bandit0"
SSH_PASSWORD="bandit0"

# Flask Application Settings
DEBUG="True"
HOST="127.0.0.1"
PORT="5000"

# Logging Configuration
LOG_LEVEL="INFO"

# Choose one of these providers:
# OpenAI API Key
OPENAI_API_KEY="your_openai_api_key_here"

# OR Google Gemini API Key
GEMINI_API_KEY="your_gemini_api_key_here"

# OR OpenRouter API Key
OPENROUTER_API_KEY="your_openrouter_api_key_here"

# Preferred LLM Provider and Model
PREFERRED_LLM_PROVIDER="ollama"
PREFERRED_LLM_MODEL="qwen2.5:1b"
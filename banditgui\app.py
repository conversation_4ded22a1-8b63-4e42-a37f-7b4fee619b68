"""Main Flask application for BanditGUI.

This module initializes the Flask application and defines the API routes.
"""

import os
import secrets
import sys
from datetime import timedelta

from dotenv import load_dotenv
from flask import Flask, jsonify, render_template, request, send_from_directory, session
from litellm import completion

from banditgui.chat.chat_manager import Chat<PERSON>anager
from banditgui.config.logging_config import logger, api_logger, get_logger
from banditgui.config.settings import config
from banditgui.ssh.ssh_manager import SSHManager
from banditgui.terminal.terminal_manager import TerminalManager
from banditgui.utils.csrf import csrf
from banditgui.utils.progression_manager import ProgressionManager
from banditgui.utils.quotes import get_random_quote, get_terminal_welcome_quotes
from banditgui.utils.rate_limiter import rate_limiter
from flask_session import Session

# Load environment variables
load_dotenv()

# Initialize Flask app with security features
app = Flask(__name__)

# Configure session settings for security
app.config["SECRET_KEY"] = os.getenv("FLASK_SECRET_KEY", secrets.token_hex(32))
app.config["SESSION_TYPE"] = "filesystem"  # Configure session type
app.config["SESSION_COOKIE_SECURE"] = True
app.config["SESSION_COOKIE_HTTPONLY"] = True
app.config["SESSION_COOKIE_SAMESITE"] = "Lax"
app.config["PERMANENT_SESSION_LIFETIME"] = timedelta(minutes=30)
app.config["SESSION_REFRESH_EACH_REQUEST"] = True

# Initialize session and CSRF protection
Session(app)
csrf.init_app(app)

# Initialize managers
ssh_manager = SSHManager()
terminal_manager = TerminalManager(ssh_manager=ssh_manager)
chat_manager = ChatManager()
progression_manager = ProgressionManager()

logger.info("BanditGUI application initialized")


@app.route("/")
def home():
    """Render the home page."""
    logger.debug("Rendering home page")
    return render_template("index.html")


@app.route("/server-status", methods=["GET"])
def server_status():
    """Check the status of the SSH server."""
    logger.info("Checking server status")
    try:
        status = ssh_manager.check_server_status()
        logger.info(f"Server status: {status['status']}")
        return jsonify({"status": "success", "serverStatus": status})
    except Exception as e:
        error_msg = f"Error checking server status: {str(e)}"
        logger.error(error_msg)
        return jsonify({"status": "error", "message": error_msg})


@app.route("/connect", methods=["POST"])
@rate_limiter.limit(
    max_tokens=5, refill_rate=0.1, per_endpoint=True
)  # Limit to 5 requests per minute
@csrf.validate_token_decorator
def connect():
    """Connect to the SSH server."""
    logger.info("Received connect request")
    try:
        result = ssh_manager.connect()
        if result is True:
            logger.info("SSH connection successful")
            # Set the current level to 0 (initial level)
            terminal_manager.current_level = 0
            return jsonify(
                {
                    "status": "success",
                    "message": "Connected to SSH server",
                    "currentLevel": terminal_manager.current_level,
                    "csrfToken": session.get(
                        "_csrf_token"
                    ),  # Return CSRF token for next request
                }
            )
        logger.error(f"SSH connection failed: {result}")
        return jsonify({"status": "error", "message": result})
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        logger.error(f"Exception during SSH connection: {error_msg}")
        return jsonify(
            {"status": "error", "message": error_msg, "errorType": type(e).__name__}
        )


@app.route("/disconnect", methods=["POST"])
@rate_limiter.limit(
    max_tokens=10, refill_rate=0.5, per_endpoint=True
)  # Limit to 10 requests per 2 minutes
@csrf.validate_token_decorator
def disconnect():
    """Disconnect from the SSH server."""
    logger.info("Received disconnect request")
    try:
        ssh_manager.close()
        terminal_manager.ssh_connected = False
        terminal_manager.current_level = None
        logger.info("SSH connection closed")
        return jsonify(
            {
                "status": "success",
                "message": "Disconnected from SSH server",
                "csrfToken": session.get(
                    "_csrf_token"
                ),  # Return CSRF token for next request
            }
        )
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        logger.error(f"Exception during SSH disconnection: {error_msg}")
        return jsonify(
            {"status": "error", "message": error_msg, "errorType": type(e).__name__}
        )


@app.route("/execute", methods=["POST"])
@rate_limiter.limit(max_tokens=10, refill_rate=0.5, per_endpoint=True)
@csrf.validate_token_decorator
def execute():
    """Execute a command."""
    command = request.json.get("command")
    username = request.json.get("username")
    password = request.json.get("password")
    # Validate command for basic security
    if not command or len(command.strip()) == 0:
        return jsonify({"status": "error", "message": "Empty command not allowed"})

    # Enhanced command injection protection
    dangerous_patterns = [';', '&&', '||', '>', '<', '`', '$', '$(', '${', '\n', '\r']
    # Allow single pipe for legitimate commands like 'ls | grep'
    if any(pattern in command for pattern in dangerous_patterns if pattern != '|'):
        logger.warning(f"Potentially dangerous command blocked: {command}")
        return jsonify({"status": "error", "message": "Command contains potentially dangerous characters"})
    
    # Check for multiple pipes which could indicate command chaining
    if command.count('|') > 2:
        logger.warning(f"Command with multiple pipes blocked: {command}")
        return jsonify({"status": "error", "message": "Complex command chaining not allowed"})
    
    # Whitelist approach for SSH commands
    if command.strip().startswith('ssh') and not command.startswith('ssh bandit'):
        logger.warning(f"Non-bandit SSH command blocked: {command}")
        return jsonify({"status": "error", "message": "Only SSH connections to bandit server are allowed"})

    logger.info(f"Executing command: {command}")
    output = terminal_manager.execute_command(
        command, username=username, password=password
    )

    if output == "<clear>":
        logger.debug("Clear command executed")
        return jsonify({"status": "clear"})

    # Check for password in output
    password_found = None
    next_level = None
    try:
        from banditgui.utils.password_detection import detect_password_from_output

        password_found = detect_password_from_output(output)
        if password_found is not None:
            # If current level is set, next level is +1
            next_level = (
                terminal_manager.current_level + 1
                if terminal_manager.current_level is not None
                else None
            )
            logger.info(
                f"Password for next level detected: {password_found} (next level: {next_level})"
            )
    except ImportError as e:
        logger.error(f"Password detection module not found: {e}")
    except Exception as e:
        logger.warning(f"Password detection failed: {e}")

    # Check if this is a level change command (<NAME_EMAIL>)
    # Parse SSH commands to detect level changes
    if command.startswith("ssh bandit") and "@" in command:
        try:
            # Extract the level number from the command
            level_part = command.split("bandit")[1].split("@")[0]
            new_level = int(level_part)
            terminal_manager.current_level = new_level
            logger.info(f"Level changed to {new_level}")
        except (ValueError, IndexError):
            # If we can't parse the level, ignore it
            pass

    # Process output to handle ANSI color codes for xterm.js
    # xterm.js can handle ANSI color codes directly, so we don't need to convert them
    logger.debug(f"Command executed, output length: {len(output)}")
    return jsonify(
        {
            "status": "success",
            "output": output,
            "currentLevel": terminal_manager.current_level,
            "passwordFound": password_found,
            "nextLevel": next_level,
        }
    )


@app.route("/static/js/<path:filename>")
def serve_js(filename):
    """Serve JavaScript files."""
    logger.debug(f"Serving JS file: {filename}")
    return send_from_directory("static/js", filename)


@app.route("/config/<path:filename>")
def serve_config_json(filename):
    logger.debug(f"Serving config JSON file: {filename}")
    # Ensure the path is safe and only serves expected files
    if filename == "llm_model.json":
        # 'config' is relative to app.root_path, which is 'banditgui/'
        return send_from_directory("config", filename, mimetype="application/json")
    else:
        return jsonify({"status": "error", "message": "File not found"}), 404


@app.route("/ask-a-pro", methods=["POST"])
@rate_limiter.limit(
    max_tokens=3, refill_rate=0.05, per_endpoint=True
)  # Limit to 3 requests per minute
@csrf.validate_token_decorator
def ask_a_pro():
    # Defensive session check
    if not session or "_csrf_token" not in session:
        logger.warning("Ask-a-Pro request without valid session.")
        return jsonify(
            {"status": "error", "message": "Invalid or missing CSRF token"}
        ), 400
    # Validate request
    if not request.is_json:
        return jsonify({"status": "error", "message": "Request must be JSON"}), 400

    try:
        data = request.get_json()
        if not isinstance(data, dict):
            raise ValueError("Invalid JSON data format")

        selected_llm_value = data.get("llm")
        level_name = data.get("level_name")
        level_description = data.get("level_description")
        command_history_list = data.get("command_history", [])

        # Validate input types
        if (
            not isinstance(selected_llm_value, str)
            or not isinstance(level_name, (str, int))
            or not isinstance(level_description, str)
            or not isinstance(command_history_list, list)
        ):
            raise ValueError("Invalid data types in request")

        # Validate command history
        if not all(isinstance(cmd, str) for cmd in command_history_list):
            raise ValueError("Command history must contain only strings")

    except Exception as e:
        logger.error(f"Invalid ask-a-pro request: {str(e)}")
        return jsonify({"status": "error", "message": "Invalid request format"}), 400

    # Basic validation
    if not all(
        [selected_llm_value, level_name is not None, level_description]
    ):  # level_name can be 0
        logger.warning("Ask-a-Pro request missing required data.")
        return jsonify(
            {"status": "error", "message": "Missing required data for Ask-a-Pro."}
        ), 400

    command_history_str = "\n".join([f"- {cmd}" for cmd in command_history_list])

    prompt_template = f"""
You are an expert mentor assisting a CTF or security challenge participant. The user is currently on a specific level, and has executed the following commands so far.

Goal: Help them understand their current situation. Provide some technical explanation, suggest a direction or next step, and recommend a resource to learn more. Never reveal the full solution. Keep it constructive and educational.

---

📍 Level Name: {level_name}
🧩 Level Description: {level_description}
📜 Command History:
{command_history_str or "No commands executed yet."}

---

🎓 Based on this, summarize what's going on, explain any key concepts, suggest something to try next, and link to a learning resource (like a man page, article, or tool documentation). The user should walk away more informed, but not with the answer directly. Never provide a complete solution
    """

    logger.debug(f"Ask-a-Pro prompt constructed for LLM: {selected_llm_value}")
    # logger.debug(f"Prompt content:\n{prompt_template}") # Uncomment for debugging prompt

    try:
        model_parts = selected_llm_value.split("/", 1)
        provider = model_parts[0]

        # Default model_name_for_api to the full value, adjust as needed
        model_name_for_api = selected_llm_value

        from banditgui.utils.api_key_manager import api_key_manager

        # Get validated API key using manager
        api_key = api_key_manager.get_key(provider.upper())

        if not api_key and provider not in ["ollama"]:
            logger.error(f"API key for {provider} is not set or invalid.")
            return jsonify(
                {
                    "status": "error",
                    "message": "API key not configured or invalid. Please contact administrator.",
                    "error_type": "api_key_missing",
                }
            ), 500

        messages_for_llm = [{"role": "user", "content": prompt_template}]

        logger.info(
            f"Sending request to LiteLLM with model: {model_name_for_api} for provider {provider}"
        )

        response = completion(
            model=model_name_for_api,  # This should be like "gpt-3.5-turbo" or "ollama/llama2" or "gemini/gemini-pro"
            messages=messages_for_llm,
            api_key=api_key,  # Pass None if not needed (e.g. Ollama)
            # For some providers like Azure, custom_llm_provider might be needed.
            # LiteLLM usually infers provider from model name string.
        )

        advice = response.choices[0].message.content
        logger.info(f"Received advice from LLM: {advice[:100]}...")
        return jsonify({"status": "success", "advice": advice})

    except NameError as e:  # Specifically catch if 'completion' is not defined
        if "completion" in str(e):
            logger.error(
                "LiteLLM is likely not installed. Please add 'litellm' to requirements.txt and install it."
            )
            return jsonify(
                {
                    "status": "error",
                    "message": "LLM integration library (LiteLLM) not available on server. Please install dependencies.",
                }
            ), 500
        else:
            error_msg = f"Unexpected NameError: {str(e)}"
            logger.error(error_msg)
            return jsonify({"status": "error", "message": error_msg}), 500
    except Exception as e:
        error_msg = f"Error calling LLM: {str(e)}"
        logger.error(error_msg, exc_info=True)  # Log full traceback for other errors
        return jsonify({"status": "error", "message": error_msg}), 500


@app.route("/level-info", methods=["POST"])
@rate_limiter.limit(max_tokens=5, refill_rate=0.2, per_endpoint=True)
def level_info():
    """Get information about a specific level."""
    level = request.json.get("level", 0)
    logger.info(f"Requested level info for level {level}")

    try:
        from banditgui.utils.level_info import get_level_info

        level_data = get_level_info(level)

        if level_data:
            logger.debug(f"Found level info for level {level}")
            return jsonify({"status": "success", "levelInfo": level_data})
        else:
            logger.warning(f"Level info not found for level {level}")
            return jsonify(
                {"status": "error", "message": f"Level {level} information not found"}
            )
    except Exception as e:
        error_msg = f"Error retrieving level info: {str(e)}"
        logger.error(error_msg)
        return jsonify({"status": "error", "message": error_msg})


@app.route("/chat/message", methods=["POST"])
def chat_message():
    """Add a message to the chat."""
    message = request.json.get("message")
    level = request.json.get("level")
    try:
        level = int(level)
    except (ValueError, TypeError):
        logger.warning("Invalid level value provided in chat message")
        return jsonify({"status": "error", "message": "Invalid level value"})
    is_system = request.json.get("isSystem", False)

    if not message:
        logger.warning("Chat message request with no message")
        return jsonify({"status": "error", "message": "No message provided"})

    logger.info(f"Adding chat message for level {level}")
    chat_manager.add_message(message, level, is_system)

    return jsonify({"status": "success", "message": "Message added"})


@app.route("/chat/messages", methods=["GET"])
def get_chat_messages():
    """Get chat messages."""
    level = request.args.get("level")
    count = request.args.get("count", 50, type=int)

    level = int(level) if level and level.isdigit() else None
    logger.info(f"Getting chat messages for level {level}")
    messages = chat_manager.get_messages(level, count)

    return jsonify({"status": "success", "messages": messages})


@app.route("/chat/hint", methods=["POST"])
def get_hint():
    """Get a hint for the current level."""
    level = request.json.get("level")

    if not isinstance(level, int):
        logger.warning("Hint request with invalid level")
        return jsonify({"status": "error", "message": "Invalid level provided"})

    logger.info(f"Getting hint for level {level}")
    hint = chat_manager.get_hint(level)

    # Add the hint as a system message
    chat_manager.add_message(hint, level, is_system=True)

    return jsonify({"status": "success", "hint": hint})


@app.route("/quotes/random", methods=["GET"])
def random_quote():
    """Get a random geek quote."""
    logger.debug("Getting random quote")
    try:
        quote = get_random_quote()
        return jsonify({"status": "success", "quote": quote})
    except Exception as e:
        error_msg = f"Error getting random quote: {str(e)}"
        logger.error(error_msg)
        return jsonify({"status": "error", "message": error_msg})


@app.route("/quotes/welcome", methods=["GET"])
def welcome_quotes():
    """Get quotes for terminal welcome message."""
    count = request.args.get("count", 3, type=int)
    logger.debug(f"Getting {count} welcome quotes")
    try:
        quotes = get_terminal_welcome_quotes(count)
        return jsonify({"status": "success", "quotes": quotes})
    except Exception as e:
        error_msg = f"Error getting welcome quotes: {str(e)}"
        logger.error(error_msg)
        return jsonify({"status": "error", "message": error_msg})


@app.route("/progress/session", methods=["POST"])
def start_progress_session():
    data = request.json
    session_id = data.get("session_id")
    if not session_id:
        return jsonify({"status": "error", "message": "Missing session_id"}), 400
    session = progression_manager.start_session(session_id)
    return jsonify({"status": "success", "session": session.__dict__})


@app.route("/progress/update", methods=["POST"])
def update_progress():
    data = request.json
    session_id = data.get("session_id")
    level = data.get("level")
    command = data.get("command")
    output = data.get("output")
    if not all([session_id, level, command, output]):
        return jsonify({"status": "error", "message": "Missing required fields"}), 400
    progression_manager.update_level_progress(session_id, level, command, output)
    return jsonify({"status": "success"})


@app.route("/progress/stats", methods=["GET"])
def get_stats():
    session_id = request.args.get("session_id")
    if not session_id:
        return jsonify({"status": "error", "message": "Missing session_id"}), 400
    stats = progression_manager.get_player_stats(session_id)
    if not stats:
        return jsonify({"status": "error", "message": "Session not found"}), 404
    return jsonify({"status": "success", "stats": stats.__dict__})


@app.route("/progress/achievements", methods=["GET"])
def get_achievements():
    session_id = request.args.get("session_id")
    if not session_id:
        return jsonify({"status": "error", "message": "Missing session_id"}), 400
    achievements = progression_manager.get_achievements(session_id)
    return jsonify({"status": "success", "achievements": achievements})


@app.route("/progress/detailed-stats", methods=["GET"])
def get_detailed_stats():
    session_id = request.args.get("session_id")
    if not session_id:
        return jsonify({"status": "error", "message": "Missing session_id"}), 400
    details = progression_manager.get_detailed_stats(session_id)
    if not details:
        return jsonify({"status": "error", "message": "Session not found"}), 404
    return jsonify({"status": "success", "details": details})


# --- TESTING ONLY: CSRF token generation endpoint ---
@app.route("/test-csrf-token", methods=["GET"])
def test_csrf_token():
    """Generate and return a CSRF token for testing purposes only."""
    token = csrf.generate_token()
    return jsonify({"csrf_token": token})


def main():
    """Run the Flask application."""
    # Validate configuration
    validation_error = config.validate()
    if validation_error:
        logger.error(f"Configuration error: {validation_error}")
        sys.exit(1)

    # Run the Flask app
    logger.info(f"Starting Flask app on {config.host}:{config.port}")
    app.run(debug=config.debug, host=config.host, port=config.port)


if __name__ == "__main__":
    main()

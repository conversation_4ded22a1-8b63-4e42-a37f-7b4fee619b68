"""
Custom exceptions for the BanditGUI application.
"""

class ProgressionError(Exception):
    """Base exception for progression-related errors."""
    def __init__(self, message: str, error_type: str = 'unknown', **kwargs):
        super().__init__(message)
        self.error_type = error_type
        self.details = kwargs

class SessionNotFoundError(ProgressionError):
    """Raised when a session cannot be found."""
    def __init__(self, session_id: str):
        super().__init__(
            f"Session {session_id} not found",
            error_type='session_not_found',
            session_id=session_id
        )

class InvalidLevelError(ProgressionError):
    """Raised when an invalid level number is provided."""
    def __init__(self, level: int):
        super().__init__(
            f"Invalid level number: {level}",
            error_type='invalid_level',
            level=level
        )

class ValidationError(Exception):
    """Raised when data validation fails."""
    def __init__(self, message: str, field: str = None):
        super().__init__(message)
        self.field = field